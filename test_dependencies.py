#!/usr/bin/env python3
"""
Test script to verify all dependencies are working correctly
"""

import sys
import traceback

def test_import(module_name, description=""):
    """Test importing a module and print result"""
    try:
        __import__(module_name)
        print(f"✅ {module_name} - {description}")
        return True
    except ImportError as e:
        print(f"❌ {module_name} - {description}: {e}")
        return False
    except Exception as e:
        print(f"⚠️  {module_name} - {description}: {e}")
        return False

def test_database():
    """Test database functionality"""
    try:
        from app.db import init_db, log_system_event, get_system_settings
        
        # Test database initialization
        init_db()
        print("✅ Database initialization")
        
        # Test logging
        log_system_event("Test message from dependency checker")
        print("✅ System logging")
        
        # Test settings
        settings = get_system_settings()
        print("✅ System settings")
        
        return True
    except Exception as e:
        print(f"❌ Database functionality: {e}")
        traceback.print_exc()
        return False

def main():
    print("========================================")
    print(" Biometric Attendance System")
    print(" Dependency Test")
    print("========================================")
    print()
    
    # Test core Python modules
    print("Testing Core Dependencies:")
    test_import("sqlite3", "SQLite database")
    test_import("tkinter", "GUI framework")
    test_import("threading", "Threading support")
    test_import("socket", "Network communication")
    test_import("json", "JSON processing")
    test_import("datetime", "Date/time handling")
    print()
    
    # Test third-party dependencies
    print("Testing Third-party Dependencies:")
    test_import("psutil", "System monitoring")
    test_import("werkzeug", "Web utilities")
    test_import("flask", "Web framework")
    test_import("flask_session", "Session management")
    test_import("flask_login", "Authentication")
    test_import("numpy", "Numerical computing")
    test_import("pandas", "Data analysis")
    test_import("cv2", "OpenCV computer vision")
    test_import("face_recognition", "Face recognition")
    test_import("PIL", "Image processing")
    print()
    
    # Test application modules
    print("Testing Application Modules:")
    test_import("app.db", "Database module")
    test_import("app.logger", "Logging module")
    test_import("app.config", "Configuration module")
    print()
    
    # Test database functionality
    print("Testing Database Functionality:")
    test_database()
    print()
    
    print("========================================")
    print("Dependency test completed!")
    print("If all tests passed, your system should")
    print("be ready to run the Production Control Center.")
    print("========================================")

if __name__ == "__main__":
    main()
