# db.py
import sqlite3
import os
import json
import pickle
import csv
from datetime import datetime,timedelta
from werkzeug.security import generate_password_hash

# Database path
DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'database.db')

def init_db():
    """Initialize the database with tables if they don't exist"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Create users table
        c.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create students table
        c.execute('''
        CREATE TABLE IF NOT EXISTS students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            name TEXT NOT NULL,
            matric_number TEXT UNIQUE NOT NULL,
            department TEXT,
            email TEXT,
            phone TEXT,
            face_encoding BLOB,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')

        # Create lecturers table
        c.execute('''
        CREATE TABLE IF NOT EXISTS lecturers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            name TEXT NOT NULL,
            staff_id TEXT UNIQUE NOT NULL,
            department TEXT,
            email TEXT,
            phone TEXT,
            face_encoding BLOB,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')

        # Create courses table
        c.execute('''
        CREATE TABLE IF NOT EXISTS courses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            title TEXT NOT NULL,
            department TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create lecturer_courses table for many-to-many relationship
        c.execute('''
        CREATE TABLE IF NOT EXISTS lecturer_courses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lecturer_id INTEGER NOT NULL,
            course_id INTEGER NOT NULL,
            FOREIGN KEY (lecturer_id) REFERENCES lecturers (id),
            FOREIGN KEY (course_id) REFERENCES courses (id),
            UNIQUE(lecturer_id, course_id)
        )
        ''')

        # Create student_courses table for many-to-many relationship
        c.execute('''
        CREATE TABLE IF NOT EXISTS student_courses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            course_id INTEGER NOT NULL,
            FOREIGN KEY (student_id) REFERENCES students (id),
            FOREIGN KEY (course_id) REFERENCES courses (id),
            UNIQUE(student_id, course_id)
        )
        ''')

        # Create lectures table
        c.execute('''
        CREATE TABLE IF NOT EXISTS lectures (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            course_id INTEGER NOT NULL,
            lecturer_id INTEGER NOT NULL,
            start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            duration INTEGER DEFAULT 60,
            room TEXT,
            is_active BOOLEAN DEFAULT 0,
            end_time TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses (id),
            FOREIGN KEY (lecturer_id) REFERENCES lecturers (id)
        )
        ''')

        # Create attendance table
        c.execute('''
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            lecture_id INTEGER NOT NULL,
            student_id INTEGER NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_late BOOLEAN DEFAULT 0,
            confidence REAL DEFAULT 0.0,
            FOREIGN KEY (lecture_id) REFERENCES lectures (id),
            FOREIGN KEY (student_id) REFERENCES students (id),
            UNIQUE(lecture_id, student_id)
        )
        ''')

        # Create system_logs table
        c.execute('''
        CREATE TABLE IF NOT EXISTS system_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            level TEXT NOT NULL DEFAULT 'INFO',
            message TEXT NOT NULL
        )
        ''')

        # Create system_settings table
        c.execute('''
        CREATE TABLE IF NOT EXISTS system_settings (
            setting_key TEXT PRIMARY KEY,
            setting_value TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create notifications table
        c.execute('''
        CREATE TABLE IF NOT EXISTS notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            message TEXT NOT NULL,
            type TEXT NOT NULL,
            related_id INTEGER,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_read BOOLEAN DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')

        conn.commit()  # Commit changes to the database

# Student functions
def add_student(name, matric_number, department, username, password):
    """Add a new student to the database"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # First, create a user account
        hashed_password = generate_password_hash(password)
        c.execute("""
            INSERT INTO users (username, password, role)
            VALUES (?, ?, ?)
        """, (username, hashed_password, 'student'))

        user_id = c.lastrowid

        # Then, create the student profile
        c.execute("""
            INSERT INTO students (user_id, name, matric_number, department)
            VALUES (?, ?, ?, ?)
        """, (user_id, name, matric_number, department))

        student_id = c.lastrowid

        return student_id

def update_student(student_id, name, matric_number, department):
    """Update student information"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute("UPDATE students SET name = ?, matric_number = ?, department = ? WHERE id = ?",
                 (name, matric_number, department, student_id))
        conn.commit()
        return c.rowcount > 0

def delete_student(student_id):
    """Delete a student from the database"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Get the user_id associated with this student
        c.execute("SELECT user_id, name FROM students WHERE id = ?", (student_id,))
        result = c.fetchone()

        if not result:
            return False

        user_id, name = result

        # First delete from attendance
        c.execute("DELETE FROM attendance WHERE student_id = ?", (student_id,))

        # Then delete the student
        c.execute("DELETE FROM students WHERE id = ?", (student_id,))

        # Finally delete the user account if it exists
        if user_id:
            c.execute("DELETE FROM users WHERE id = ?", (user_id,))

        log_system_event(f"Student {name} (ID: {student_id}) deleted")
        return True

def get_student_by_id(student_id):
    """Get student information by ID"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT * FROM students WHERE id = ? OR user_id = ?", (student_id, student_id))
        student = c.fetchone()
        if student:
            return dict(student)
        return None

def get_student_by_name(name):
    """Get student information by name"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        # Try exact match first, then case-insensitive
        c.execute("SELECT * FROM students WHERE name = ? OR LOWER(name) = LOWER(?)", (name, name))
        student = c.fetchone()
        if student:
            return dict(student)
        return None

def get_all_students():
    """Get all students from the database"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("""
            SELECT s.id, s.name, s.matric_number, s.department,
                   s.email, s.phone,
                   CASE WHEN s.face_encoding IS NOT NULL THEN 1 ELSE 0 END as has_face,
                   u.username
            FROM students s
            LEFT JOIN users u ON s.user_id = u.id
            ORDER BY s.name
        """)
        students = [dict(row) for row in c.fetchall()]
        return students

def get_students_count():
    """Get the total number of students"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute("SELECT COUNT(*) FROM students")
        return c.fetchone()[0]

def get_student_by_user_id(user_id):
    """Get student information by user ID"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        c.execute("""
            SELECT s.id, s.name, s.matric_number, s.email, s.phone, u.username
            FROM students s
            JOIN users u ON s.user_id = u.id
            WHERE s.user_id = ?
        """, (user_id,))

        student = c.fetchone()
        if student:
            return dict(student)
        return None

def get_filtered_student_attendance(student_id, course_filter='', date_from='', date_to=''):
    """Get filtered attendance records for a specific student"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        query = """
            SELECT l.id as lecture_id, l.course_id, c.code as course_code, c.title as course_name,
                   l.start_time as lecture_date, l.duration as duration_minutes,
                   a.timestamp as attendance_time, a.is_late
            FROM attendance a
            JOIN lectures l ON a.lecture_id = l.id
            JOIN courses c ON l.course_id = c.id
            WHERE a.student_id = ?
        """

        params = [student_id]

        if course_filter:
            query += " AND (c.code LIKE ? OR c.title LIKE ?)"
            params.extend([f"%{course_filter}%", f"%{course_filter}%"])

        if date_from:
            query += " AND l.start_time >= ?"
            params.append(f"{date_from} 00:00:00")

        if date_to:
            query += " AND l.start_time <= ?"
            params.append(f"{date_to} 23:59:59")

        query += " ORDER BY l.start_time DESC"

        c.execute(query, params)
        return [dict(row) for row in c.fetchall()]

def get_student_courses(student_id):
    """Get courses that a student is enrolled in"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        c.execute("""
            SELECT DISTINCT c.id, c.code, c.title as name
            FROM courses c
            JOIN lectures l ON c.id = l.course_id
            JOIN attendance a ON l.id = a.lecture_id
            WHERE a.student_id = ?
            ORDER BY c.code
        """, (student_id,))

        return [dict(row) for row in c.fetchall()]

def get_upcoming_lectures_for_student(student_id):
    """Get upcoming lectures for a student based on enrolled courses"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        # Get courses the student is enrolled in
        c.execute("""
            SELECT DISTINCT c.id
            FROM courses c
            JOIN lectures l ON c.id = l.course_id
            JOIN attendance a ON l.id = a.lecture_id
            WHERE a.student_id = ?
        """, (student_id,))

        course_ids = [row['id'] for row in c.fetchall()]

        if not course_ids:
            return []

        # Get upcoming lectures for these courses
        placeholders = ','.join(['?'] * len(course_ids))
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        c.execute(f"""
            SELECT l.id, l.course_id, c.code as course_code, c.title as course_name,
                   l.start_time, l.duration as duration_minutes, lec.name as lecturer_name
            FROM lectures l
            JOIN courses c ON l.course_id = c.id
            JOIN lecturers lec ON l.lecturer_id = lec.id
            WHERE l.course_id IN ({placeholders})
            AND l.start_time > ?
            ORDER BY l.start_time
            LIMIT 5
        """, course_ids + [now])

        return [dict(row) for row in c.fetchall()]

# Lecturer functions
def add_lecturer(name, username, password, staff_id='', department=''):
    """Add a new lecturer to the database"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # First, create a user account
        hashed_password = generate_password_hash(password)
        c.execute("""
            INSERT INTO users (username, password, role)
            VALUES (?, ?, ?)
        """, (username, hashed_password, 'lecturer'))

        user_id = c.lastrowid

        # Then, create the lecturer profile
        c.execute("""
            INSERT INTO lecturers (user_id, name, staff_id, department)
            VALUES (?, ?, ?, ?)
        """, (user_id, name, staff_id, department))

        lecturer_id = c.lastrowid

        return lecturer_id

def update_lecturer(lecturer_id, name, staff_id=None, department=None):
    """Update lecturer information"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Build the update query dynamically based on provided fields
        update_fields = ["name = ?"]
        params = [name]

        if staff_id:
            update_fields.append("staff_id = ?")
            params.append(staff_id)

        if department:
            update_fields.append("department = ?")
            params.append(department)

        params.append(lecturer_id)  # For the WHERE clause

        query = f"UPDATE lecturers SET {', '.join(update_fields)} WHERE id = ?"
        c.execute(query, params)
        conn.commit()
        return c.rowcount > 0

def delete_lecturer(lecturer_id):
    """Delete a lecturer from the database"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Get the user_id associated with this lecturer
        c.execute("SELECT user_id FROM lecturers WHERE id = ?", (lecturer_id,))
        result = c.fetchone()

        if not result:
            return False

        user_id = result[0]

        # First delete from lecturer_courses
        c.execute("DELETE FROM lecturer_courses WHERE lecturer_id = ?", (lecturer_id,))

        # Then delete the lecturer
        c.execute("DELETE FROM lecturers WHERE id = ?", (lecturer_id,))

        # Finally delete the user account if it exists
        if user_id:
            c.execute("DELETE FROM users WHERE id = ?", (user_id,))

        return True

def get_lecturer_by_id(lecturer_id):
    """Get lecturer information by ID"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT * FROM lecturers WHERE id = ? OR user_id = ?", (lecturer_id, lecturer_id))
        lecturer = c.fetchone()
        if lecturer:
            return dict(lecturer)
        return None

def get_lecturer_by_staff_id(staff_id):
    """Get lecturer information by staff ID"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT * FROM lecturers WHERE staff_id = ?", (staff_id,))
        lecturer = c.fetchone()
        if lecturer:
            return dict(lecturer)
        return None

def get_all_lecturers():
    """Get all lecturers from the database"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("""
            SELECT l.id, l.name, l.staff_id, l.department, l.email, l.phone,
                   l.face_encoding IS NOT NULL as has_face,
                   u.username
            FROM lecturers l
            LEFT JOIN users u ON l.user_id = u.id
            ORDER BY l.name
        """)
        return [dict(row) for row in c.fetchall()]

def get_lecturers_count():
    """Get the total number of lecturers"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute("SELECT COUNT(*) FROM lecturers")
        return c.fetchone()[0]

# Lecture functions
def create_lecture(course_id, lecturer_id, start_time, duration, room=''):
    """Create a new lecture"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        c.execute("""
            INSERT INTO lectures (course_id, lecturer_id, start_time, duration, room, is_active)
            VALUES (?, ?, ?, ?, ?, 1)
        """, (course_id, lecturer_id, start_time, duration, room))

        lecture_id = c.lastrowid

        return lecture_id

def end_lecture(lecture_id):
    """Mark a lecture as inactive (ended)"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute("UPDATE lectures SET is_active = 0 WHERE id = ?", (lecture_id,))
        conn.commit()
        return c.rowcount > 0

def get_lecture_by_id(lecture_id):
    """Get a lecture by its ID"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        c.execute("""
            SELECT l.id, l.course_id, l.lecturer_id, l.start_time,
                   l.duration as duration_minutes, l.room, l.is_active, l.end_time,
                   c.code as course_code, c.title as course_name,
                   lec.name as lecturer_name
            FROM lectures l
            JOIN courses c ON l.course_id = c.id
            JOIN lecturers lec ON l.lecturer_id = lec.id
            WHERE l.id = ?
        """, (lecture_id,))

        lecture = c.fetchone()
        if lecture:
            lecture_dict = dict(lecture)
            # Convert is_active to boolean
            lecture_dict['is_active'] = bool(lecture_dict['is_active'])
            return lecture_dict
        return None

def get_all_lectures(lecturer_id=None):
    """Get all lectures from the database"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        query = """
            SELECT lec.id, lec.course_id, lec.lecturer_id, lec.start_time,
                   lec.duration as duration_minutes, lec.room, lec.is_active,
                   c.code as course_code, c.title as course_name,
                   l.name as lecturer_name,
                   (SELECT COUNT(*) FROM attendance a WHERE a.lecture_id = lec.id) as attendance_count
            FROM lectures lec
            JOIN courses c ON lec.course_id = c.id
            JOIN lecturers l ON lec.lecturer_id = l.id
        """

        params = []

        # Add lecturer_id filter if provided
        if lecturer_id:
            query += " WHERE lec.lecturer_id = ?"
            params.append(lecturer_id)

        query += " ORDER BY lec.start_time DESC"

        c.execute(query, params)
        lectures = [dict(row) for row in c.fetchall()]

        # Convert is_active to boolean
        for lecture in lectures:
            lecture['is_active'] = bool(lecture['is_active'])

        return lectures

def get_lectures_count():
    """Get the total number of lectures"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute("SELECT COUNT(*) FROM lectures")
        return c.fetchone()[0]

# Attendance functions
def log_attendance(lecture_id, matric_number):
    """Log attendance for a student in a lecture"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Get the student ID from matric number
        c.execute("SELECT id FROM students WHERE matric_number = ?", (matric_number,))
        student = c.fetchone()
        if not student:
            return False

        student_id = student[0]

        # Check if already logged for this lecture
        c.execute("SELECT id FROM attendance WHERE lecture_id = ? AND student_id = ?",
                 (lecture_id, student_id))
        if c.fetchone():
            return False  # Already logged

        # Get lecture start time and duration to check if late
        c.execute("SELECT start_time, duration_minutes FROM lectures WHERE id = ?", (lecture_id,))
        lecture = c.fetchone()
        if not lecture:
            return False

        start_time = datetime.strptime(lecture[0], "%Y-%m-%d %H:%M:%S")
        grace_period_minutes = 15  # 15 minutes grace period
        late_threshold = start_time + timedelta(minutes=grace_period_minutes)

        timestamp = datetime.now()
        is_late = timestamp > late_threshold

        # Log the attendance
        c.execute("""
            INSERT INTO attendance (lecture_id, student_id, timestamp, is_late)
            VALUES (?, ?, ?, ?)
        """, (lecture_id, student_id, timestamp.strftime("%Y-%m-%d %H:%M:%S"), is_late))
        conn.commit()
        return True

def get_lecture_attendance(lecture_id):
    """Get attendance records for a specific lecture"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("""
            SELECT s.name as student_name, s.matric_number, s.department,
                   a.timestamp, a.is_late
            FROM attendance a
            JOIN students s ON a.student_id = s.id
            WHERE a.lecture_id = ?
            ORDER BY a.timestamp
        """, (lecture_id,))
        return [dict(row) for row in c.fetchall()]

def get_student_attendance(student_id):
    """Get attendance records for a specific student"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        # First check if the ID is a user_id
        c.execute("SELECT id FROM students WHERE user_id = ?", (student_id,))
        result = c.fetchone()
        if result:
            student_id = result[0]

        c.execute("""
            SELECT l.id as lecture_id, l.course_id, c.code as course_code, c.title as course_name,
                   l.start_time as lecture_date, l.duration as duration_minutes,
                   a.timestamp as attendance_time, a.is_late
            FROM attendance a
            JOIN lectures l ON a.lecture_id = l.id
            JOIN courses c ON l.course_id = c.id
            WHERE a.student_id = ?
            ORDER BY l.start_time DESC
        """, (student_id,))
        return [dict(row) for row in c.fetchall()]

def export_attendance_csv(lecture_id, output_path):
    """Export attendance records for a lecture to CSV"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Get lecture info
        c.execute("""
            SELECT c.code as course_code, c.title as course_title,
                   l.start_time, lec.name as lecturer_name
            FROM lectures l
            JOIN courses c ON l.course_id = c.id
            JOIN lecturers lec ON l.lecturer_id = lec.id
            WHERE l.id = ?
        """, (lecture_id,))
        lecture = c.fetchone()

        if not lecture:
            return False

        # Get attendance records
        c.execute("""
            SELECT s.name, s.matric_number, s.department, a.timestamp, a.is_late
            FROM attendance a
            JOIN students s ON a.student_id = s.id
            WHERE a.lecture_id = ?
            ORDER BY s.name
        """, (lecture_id,))
        attendance_records = c.fetchall()

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Write to CSV
        with open(output_path, 'w', newline='') as f:
            writer = csv.writer(f)
            # Header with lecture info
            writer.writerow(['Course', 'Date', 'Lecturer'])
            writer.writerow([f"{lecture[0]} - {lecture[1]}", lecture[2], lecture[3]])
            writer.writerow([])  # Empty row

            # Attendance data
            writer.writerow(['Student Name', 'Matric Number', 'Department', 'Time', 'Status'])
            for record in attendance_records:
                writer.writerow([
                    record[0],  # name
                    record[1],  # matric_number
                    record[2],  # department
                    record[3],  # timestamp
                    'Late' if record[4] else 'On Time'
                ])

        return True

# User functions
def add_user(username, password, role):
    """Add a new user to the database"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        # Changed from password_hash to password
        c.execute("INSERT INTO users (username, password, role) VALUES (?, ?, ?)",
                 (username, password, role))
        conn.commit()
        return c.lastrowid

def get_user_by_username(username):
    """Get user information by username"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT * FROM users WHERE username = ?", (username,))
        user = c.fetchone()
        if user:
            return dict(user)
        return None

def get_user_by_id(user_id):
    """Get user by ID"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT * FROM users WHERE id = ?", (user_id,))
        user = c.fetchone()
        if user:
            return dict(user)
        return None

def get_users_by_role(role):
    """Get all users with a specific role"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        c.execute("""
            SELECT id, username, role, created_at
            FROM users
            WHERE role = ?
            ORDER BY username
        """, (role,))

        return [dict(row) for row in c.fetchall()]

# Course management functions
def add_course(code, title, department=''):
    """Add a new course to the database"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        c.execute("""
            INSERT INTO courses (code, title, department)
            VALUES (?, ?, ?)
        """, (code, title, department))

        course_id = c.lastrowid

        return course_id

def update_course(course_id, code=None, title=None, department=None):
    """Update course information"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Build the update query dynamically based on provided fields
        update_fields = []
        params = []

        if code:
            update_fields.append("code = ?")
            params.append(code)

        if title:
            update_fields.append("title = ?")
            params.append(title)

        if department:
            update_fields.append("department = ?")
            params.append(department)

        if not update_fields:
            return False  # Nothing to update

        params.append(course_id)  # For the WHERE clause

        query = f"UPDATE courses SET {', '.join(update_fields)} WHERE id = ?"
        c.execute(query, params)
        conn.commit()
        return c.rowcount > 0

def delete_course(course_id):
    """Delete a course from the database"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute("DELETE FROM courses WHERE id = ?", (course_id,))
        conn.commit()
        return c.rowcount > 0

def get_course_by_id(course_id):
    """Get course information by ID"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT * FROM courses WHERE id = ?", (course_id,))
        course = c.fetchone()
        if course:
            return dict(course)
        return None

def get_course_by_code(course_code):
    """Get course information by course code"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT * FROM courses WHERE code = ?", (course_code,))
        course = c.fetchone()
        if course:
            return dict(course)
        return None

def get_all_courses():
    """Get all courses from the database"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT * FROM courses ORDER BY code")
        return [dict(row) for row in c.fetchall()]

# Lecturer-Course assignment functions
def assign_lecturer_to_course(lecturer_id, course_id):
    """Assign a lecturer to a course"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        try:
            c.execute("INSERT INTO lecturer_courses (lecturer_id, course_id) VALUES (?, ?)",
                     (lecturer_id, course_id))
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            # Already assigned
            return False

def remove_lecturer_from_course(lecturer_id, course_id):
    """Remove a lecturer from a course"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute("DELETE FROM lecturer_courses WHERE lecturer_id = ? AND course_id = ?",
                 (lecturer_id, course_id))
        conn.commit()
        return c.rowcount > 0

def get_lecturer_courses(lecturer_id):
    """Get all courses taught by a specific lecturer"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        c.execute("""
            SELECT c.id, c.code, c.title, c.department
            FROM courses c
            JOIN lecturer_courses lc ON c.id = lc.course_id
            WHERE lc.lecturer_id = ?
            ORDER BY c.code
        """, (lecturer_id,))

        return [dict(row) for row in c.fetchall()]

def get_course_lecturers(course_id):
    """Get all lecturers assigned to a course"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("""
            SELECT l.id, l.name, l.staff_id, l.department
            FROM lecturers l
            JOIN lecturer_courses lc ON l.id = lc.lecturer_id
            WHERE lc.course_id = ?
            ORDER BY l.name
        """, (course_id,))
        return [dict(row) for row in c.fetchall()]

def import_faces_to_db():
    """Import face encodings from images directory to database"""
    import os
    import face_recognition
    import pickle
    import re
    from logger import log_system_event

    imported_count = {'students': 0, 'lecturers': 0}

    # Import student faces
    student_dir = "./images/student_faces"
    if os.path.exists(student_dir):
        # Process each student folder
        for student_folder in os.listdir(student_dir):
            folder_path = os.path.join(student_dir, student_folder)
            if not os.path.isdir(folder_path):
                continue

            # Check for metadata file
            metadata_path = os.path.join(folder_path, "metadata.json")
            if not os.path.exists(metadata_path):
                metadata_path = os.path.join(folder_path, "metadata.txt")
                if not os.path.exists(metadata_path):
                    log_system_event(f"No metadata found in {folder_path}", "WARNING")
                    continue

            # Extract student info from folder name or metadata
            try:
                # Try to parse from metadata file first
                import json
                try:
                    with open(metadata_path, 'r') as f:
                        if metadata_path.endswith('.json'):
                            metadata = json.load(f)
                            name = metadata.get('name', '')
                            matric_number = metadata.get('id', '')  # This will have the original format with slashes
                            role = metadata.get('role', 'Student')
                        else:
                            # Parse text metadata
                            lines = f.readlines()
                            metadata = {}
                            for line in lines:
                                if ':' in line:
                                    key, value = line.split(':', 1)
                                    metadata[key.strip()] = value.strip()
                            name = metadata.get('Name', '')
                            matric_number = metadata.get('ID Number', '')
                            role = metadata.get('Role', 'Student')
                except Exception as e:
                    # Fallback to folder name parsing
                    parts = student_folder.split('_')
                    if len(parts) >= 2:
                        sanitized_id = parts[0]
                        # Try to restore original format if it looks like a sanitized matric number
                        # Pattern: AUL_XXX_YY_NNNNN where XXX is department code, YY is year
                        matric_pattern = re.match(r'AUL_([A-Z]+)_(\d+)_(\d+)', sanitized_id)
                        if matric_pattern:
                            dept = matric_pattern.group(1)
                            year = matric_pattern.group(2)
                            number = matric_pattern.group(3)
                            matric_number = f"AUL/{dept}/{year}/{number}"
                        else:
                            matric_number = sanitized_id
                        name = parts[1].replace('_', ' ')
                        role = 'Student'  # Default role
                    else:
                        log_system_event(f"Could not parse student info from {folder_path}: {str(e)}", "WARNING")
                        continue

                # Skip if this is actually a lecturer
                if role.lower() == 'lecturer':
                    log_system_event(f"Skipping lecturer in student directory: {name}", "WARNING")
                    continue

                # Find RGB images
                image_files = [f for f in os.listdir(folder_path)
                              if f.lower().endswith(('.jpg', '.jpeg', '.png'))
                              and not f.lower().startswith('ir_')]

                if not image_files:
                    log_system_event(f"No images found in {folder_path}", "WARNING")
                    continue

                # Process the first image
                image_path = os.path.join(folder_path, image_files[0])
                image = face_recognition.load_image_file(image_path)
                face_encodings = face_recognition.face_encodings(image)

                if not face_encodings:
                    log_system_event(f"No face found in {image_path}", "WARNING")
                    continue

                face_encoding = face_encodings[0]

                # Store in database
                with sqlite3.connect(DB_PATH) as conn:
                    c = conn.cursor()

                    # Check if student exists
                    c.execute("SELECT id FROM students WHERE name = ? OR matric_number = ?",
                             (name, matric_number))
                    student = c.fetchone()

                    if student:
                        # Update existing student
                        student_id = student[0]
                        # Convert numpy array to binary blob
                        encoding_blob = pickle.dumps(face_encoding)
                        c.execute("UPDATE students SET face_encoding = ? WHERE id = ?",
                                 (encoding_blob, student_id))
                        log_system_event(f"Updated face encoding for student: {name} ({matric_number})")
                    else:
                        # Add new student
                        # Convert numpy array to binary blob
                        encoding_blob = pickle.dumps(face_encoding)
                        c.execute("""
                            INSERT INTO students (name, matric_number, face_encoding)
                            VALUES (?, ?, ?)
                        """, (name, matric_number, encoding_blob))
                        log_system_event(f"Added new student with face encoding: {name} ({matric_number})")

                    imported_count['students'] += 1
            except Exception as e:
                log_system_event(f"Error processing student folder {folder_path}: {str(e)}", "ERROR")

    # Import lecturer faces
    lecturer_dir = "./images/lecturer_faces"
    if os.path.exists(lecturer_dir):
        # Process each lecturer folder
        for lecturer_folder in os.listdir(lecturer_dir):
            folder_path = os.path.join(lecturer_dir, lecturer_folder)
            if not os.path.isdir(folder_path):
                continue

            # Check for metadata file
            metadata_path = os.path.join(folder_path, "metadata.json")
            if not os.path.exists(metadata_path):
                metadata_path = os.path.join(folder_path, "metadata.txt")
                if not os.path.exists(metadata_path):
                    log_system_event(f"No metadata found in {folder_path}", "WARNING")
                    continue

            # Extract lecturer info from folder name or metadata
            try:
                # Try to parse from metadata file first
                import json
                try:
                    with open(metadata_path, 'r') as f:
                        if metadata_path.endswith('.json'):
                            metadata = json.load(f)
                            name = metadata.get('name', '')
                            staff_id = metadata.get('id', '')
                        else:
                            # Parse text metadata
                            lines = f.readlines()
                            metadata = {}
                            for line in lines:
                                if ':' in line:
                                    key, value = line.split(':', 1)
                                    metadata[key.strip()] = value.strip()
                            name = metadata.get('Name', '')
                            staff_id = metadata.get('ID Number', '')
                except:
                    # Fallback to folder name parsing
                    parts = lecturer_folder.split('_')
                    if len(parts) >= 2:
                        name = parts[0].replace('-', ' ')
                        staff_id = parts[1]
                    else:
                        log_system_event(f"Could not parse lecturer info from {folder_path}", "WARNING")
                        continue

                # Find RGB images
                image_files = [f for f in os.listdir(folder_path)
                              if f.lower().endswith(('.jpg', '.jpeg', '.png'))
                              and not f.lower().startswith('ir_')]

                if not image_files:
                    log_system_event(f"No images found in {folder_path}", "WARNING")
                    continue

                # Process the first image
                image_path = os.path.join(folder_path, image_files[0])
                image = face_recognition.load_image_file(image_path)
                face_encodings = face_recognition.face_encodings(image)

                if not face_encodings:
                    log_system_event(f"No face found in {image_path}", "WARNING")
                    continue

                face_encoding = face_encodings[0]

                # Store in database
                with sqlite3.connect(DB_PATH) as conn:
                    c = conn.cursor()

                    # Check if lecturer exists
                    c.execute("SELECT id FROM lecturers WHERE name = ? OR staff_id = ?",
                             (name, staff_id))
                    lecturer = c.fetchone()

                    if lecturer:
                        # Update existing lecturer
                        lecturer_id = lecturer[0]
                        # Convert numpy array to binary blob
                        encoding_blob = pickle.dumps(face_encoding)
                        c.execute("UPDATE lecturers SET face_encoding = ? WHERE id = ?",
                                 (encoding_blob, lecturer_id))
                        log_system_event(f"Updated face encoding for lecturer: {name} ({staff_id})")
                    else:
                        # Add new lecturer
                        # Convert numpy array to binary blob
                        encoding_blob = pickle.dumps(face_encoding)
                        c.execute("""
                            INSERT INTO lecturers (name, staff_id, face_encoding)
                            VALUES (?, ?, ?)
                        """, (name, staff_id, encoding_blob))
                        log_system_event(f"Added new lecturer with face encoding: {name} ({staff_id})")

                    imported_count['lecturers'] += 1
            except Exception as e:
                log_system_event(f"Error processing lecturer folder {folder_path}: {str(e)}", "ERROR")

    log_system_event(f"Import complete: {imported_count['students']} students and {imported_count['lecturers']} lecturers processed")
    return imported_count

# System settings functions
def get_system_settings():
    """Get all system settings"""
    settings_dict = {}

    try:
        with sqlite3.connect(DB_PATH) as conn:
            conn.row_factory = sqlite3.Row
            c = conn.cursor()

            # Get all settings from system_settings table
            c.execute("SELECT setting_key, setting_value FROM system_settings")
            rows = c.fetchall()

            # List of keys that might conflict with Flask's built-in template variables
            conflicting_keys = ['csrf_token', 'request', 'session', 'g', 'url_for']

            for row in rows:
                key = row['setting_key']
                value = row['setting_value']

                # Skip conflicting keys
                if key in conflicting_keys:
                    print(f"Warning: Skipping conflicting setting key: {key}")
                    continue

                # Convert string values to appropriate types
                if key in ['late_threshold_minutes', 'email_port']:
                    try:
                        settings_dict[key] = int(value)
                    except:
                        settings_dict[key] = value
                elif key in ['confidence_threshold']:
                    try:
                        settings_dict[key] = float(value)
                    except:
                        settings_dict[key] = value
                elif key in ['enable_email_notifications', 'notify_absences', 'notify_spoofing']:
                    settings_dict[key] = value.lower() == 'true'
                else:
                    settings_dict[key] = value

            return settings_dict

    except Exception as e:
        print(f"Error getting system settings: {e}")
        # Return default settings
        return {
            'late_threshold_minutes': 15,
            'confidence_threshold': 0.85,
            'enable_email_notifications': False,
            'email_server': '',
            'email_port': 587,
            'email_username': '',
            'email_password': '',
            'notify_absences': True,
            'notify_spoofing': True,
            'depth_range_mm': (500, 3000),
            'ir_reflectance_threshold': 1000
        }

def update_system_setting(key, value):
    """Update a system setting"""
    try:
        with sqlite3.connect(DB_PATH) as conn:
            c = conn.cursor()

            # Create settings table if it doesn't exist
            c.execute('''
            CREATE TABLE IF NOT EXISTS system_settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # Convert lists to JSON strings
            if isinstance(value, list):
                value = json.dumps(value)

            # Insert or update the setting
            c.execute('''
            INSERT OR REPLACE INTO system_settings (key, value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
            ''', (key, value))

            return True
    except Exception as e:
        print(f"Error updating system setting: {e}")
        return False

def update_config_file(settings):
    """Update the configuration file with new settings"""
    try:
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.json')

        # Read existing config if it exists
        config = {}
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
            except:
                # If reading fails, start with empty config
                config = {}

        # Update config with new settings
        for key, value in settings.items():
            # Convert string values to appropriate types
            if key in ['grace_period_minutes', 'attendance_threshold', 'exit_timeout_seconds', 'email_port']:
                try:
                    config[key] = int(value)
                except:
                    config[key] = value
            elif key in ['detection_confidence']:
                try:
                    config[key] = float(value)
                except:
                    config[key] = value
            elif key in ['enable_email_notifications', 'notify_absences', 'notify_spoofing', 'enable_sms_notifications']:
                config[key] = value.lower() == 'true'
            else:
                config[key] = value

        # Write updated config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=4)

        return True
    except Exception as e:
        print(f"Error updating config file: {e}")
        return False

def delete_old_attendance_records(date_str):
    """Delete attendance records older than the given date"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Convert date string to datetime
        from datetime import datetime
        cutoff_date = datetime.strptime(date_str, '%Y-%m-%d').strftime('%Y-%m-%d 00:00:00')

        # Get lectures older than the cutoff date
        c.execute("SELECT id FROM lectures WHERE start_time < ?", (cutoff_date,))
        old_lecture_ids = [row[0] for row in c.fetchall()]

        if not old_lecture_ids:
            return 0

        # Delete attendance records for these lectures
        placeholders = ','.join(['?'] * len(old_lecture_ids))
        c.execute(f"DELETE FROM attendance WHERE lecture_id IN ({placeholders})", old_lecture_ids)
        deleted_count = c.rowcount

        # Optionally, also delete the lecture records
        c.execute(f"DELETE FROM lectures WHERE id IN ({placeholders})", old_lecture_ids)

        conn.commit()
        return deleted_count

def store_student(name, matric_number, face_encoding):
    """Store a student's face encoding in the database"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Check if student exists
        c.execute("SELECT id FROM students WHERE name = ? OR matric_number = ?",
                 (name, matric_number))
        student = c.fetchone()

        if student:
            # Update existing student
            student_id = student[0]
            # Convert numpy array to binary blob
            encoding_blob = pickle.dumps(face_encoding)
            c.execute("UPDATE students SET face_encoding = ? WHERE id = ?",
                     (encoding_blob, student_id))
            log_system_event(f"Updated face encoding for student: {name} ({matric_number})")
        else:
            # Add new student
            # Convert numpy array to binary blob
            encoding_blob = pickle.dumps(face_encoding)
            c.execute("""
                INSERT INTO students (name, matric_number, face_encoding)
                VALUES (?, ?, ?)
            """, (name, matric_number, encoding_blob))
            student_id = c.lastrowid
            log_system_event(f"Added new student with face encoding: {name} ({matric_number})")

        conn.commit()
        return student_id

def store_lecturer(name, staff_id, face_encoding):
    """Store a lecturer's face encoding in the database"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Check if lecturer exists
        c.execute("SELECT id FROM lecturers WHERE name = ? OR staff_id = ?",
                 (name, staff_id))
        lecturer = c.fetchone()

        if lecturer:
            # Update existing lecturer
            lecturer_id = lecturer[0]
            # Convert numpy array to binary blob
            encoding_blob = pickle.dumps(face_encoding)
            c.execute("UPDATE lecturers SET face_encoding = ? WHERE id = ?",
                     (encoding_blob, lecturer_id))
            log_system_event(f"Updated face encoding for lecturer: {name} ({staff_id})")
        else:
            # Add new lecturer
            # Convert numpy array to binary blob
            encoding_blob = pickle.dumps(face_encoding)
            c.execute("""
                INSERT INTO lecturers (name, staff_id, face_encoding)
                VALUES (?, ?, ?)
            """, (name, staff_id, encoding_blob))
            lecturer_id = c.lastrowid
            log_system_event(f"Added new lecturer with face encoding: {name} ({staff_id})")

        conn.commit()
        return lecturer_id

# Notification functions
def create_notification(user_id, message, notification_type, related_id=None, is_read=False):
    """Create a new notification for a user"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        c.execute("""
            INSERT INTO notifications (user_id, message, type, related_id, timestamp, is_read)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (user_id, message, notification_type, related_id, timestamp, is_read))

        conn.commit()
        return c.lastrowid

def get_user_notifications(user_id, limit=20, include_read=False):
    """Get notifications for a specific user"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        query = """
            SELECT id, message, type, related_id, timestamp, is_read
            FROM notifications
            WHERE user_id = ?
        """

        if not include_read:
            query += " AND is_read = 0"

        query += " ORDER BY timestamp DESC LIMIT ?"

        c.execute(query, (user_id, limit))
        return [dict(row) for row in c.fetchall()]

def mark_notification_as_read(notification_id):
    """Mark a notification as read"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        c.execute("UPDATE notifications SET is_read = 1 WHERE id = ?", (notification_id,))
        conn.commit()
        return c.rowcount > 0

def mark_all_notifications_as_read(user_id):
    """Mark all notifications for a user as read"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        c.execute("UPDATE notifications SET is_read = 1 WHERE user_id = ?", (user_id,))
        conn.commit()
        return c.rowcount

def delete_notification(notification_id):
    """Delete a notification"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        c.execute("DELETE FROM notifications WHERE id = ?", (notification_id,))
        conn.commit()
        return c.rowcount > 0

def create_absence_notification(student_id, lecture_id):
    """Create an absence notification for a student"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        # Get student and lecture info
        c.execute("""
            SELECT s.name as student_name, s.user_id,
                   l.course_code, l.start_time
            FROM students s, lectures l
            WHERE s.id = ? AND l.id = ?
        """, (student_id, lecture_id))

        result = c.fetchone()
        if not result:
            return False

        # Create notification
        message = f"You were absent from {result['course_code']} lecture on {result['start_time'].split(' ')[0]}"
        create_notification(result['user_id'], message, 'absence', lecture_id)

        return True

def send_sms_notification(phone_number, message):
    """Send SMS notification using Twilio or WhatsApp Business API"""
    settings = get_system_settings()

    if settings.get('enable_sms_notifications') != 'true':
        log_system_event(f"SMS notification skipped (disabled): {message}")
        return False

    provider = settings.get('sms_provider', 'twilio')
    api_key = settings.get('sms_api_key', '')
    api_secret = settings.get('sms_api_secret', '')
    from_number = settings.get('sms_from_number', '')

    if not all([api_key, api_secret, from_number]):
        log_system_event(f"SMS notification failed: Missing configuration", "ERROR")
        return False

    try:
        if provider == 'twilio':
            # Twilio integration
            from twilio.rest import Client
            client = Client(api_key, api_secret)

            client.messages.create(
                body=message,
                from_=from_number,
                to=phone_number
            )
            log_system_event(f"SMS sent to {phone_number} via Twilio")
            return True

        elif provider == 'whatsapp':
            # WhatsApp Business API integration
            from twilio.rest import Client
            client = Client(api_key, api_secret)

            # For WhatsApp via Twilio, format numbers as "whatsapp:+**********"
            to_whatsapp = f"whatsapp:{phone_number}" if not phone_number.startswith("whatsapp:") else phone_number
            from_whatsapp = from_number if from_number.startswith("whatsapp:") else f"whatsapp:{from_number}"

            client.messages.create(
                body=message,
                from_=from_whatsapp,
                to=to_whatsapp
            )
            log_system_event(f"WhatsApp message sent to {phone_number}")
            return True

        else:
            log_system_event(f"Unknown SMS provider: {provider}", "ERROR")
            return False

    except Exception as e:
        log_system_event(f"Error sending SMS notification: {str(e)}", "ERROR")
        return False

def send_email_notification(email, subject, message):
    """Send email notification"""
    # This is a placeholder - implement with actual email service
    try:
        # Example with smtplib
        # import smtplib
        # from email.mime.text import MIMEText
        # from email.mime.multipart import MIMEMultipart

        # msg = MIMEMultipart()
        # msg['From'] = EMAIL_SENDER
        # msg['To'] = email
        # msg['Subject'] = subject
        # msg.attach(MIMEText(message, 'plain'))

        # server = smtplib.SMTP(EMAIL_SERVER, EMAIL_PORT)
        # server.starttls()
        # server.login(EMAIL_USERNAME, EMAIL_PASSWORD)
        # server.send_message(msg)
        # server.quit()

        # For now, just log the message
        log_system_event(f"Email notification to {email}: {subject} - {message}")
        return True
    except Exception as e:
        log_system_event(f"Error sending email notification: {str(e)}", "ERROR")
        return False

def get_students_enrolled_in_course(course_id):
    """Get all students enrolled in a specific course"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        c.execute("""
            SELECT s.id, s.name, s.matric_number, s.user_id, s.email, s.phone,
                   (SELECT setting_value FROM user_preferences
                    WHERE user_id = s.user_id AND setting_key = 'email_notifications') as email_notifications,
                   (SELECT setting_value FROM user_preferences
                    WHERE user_id = s.user_id AND setting_key = 'sms_notifications') as sms_notifications
            FROM students s
            JOIN student_courses sc ON s.id = sc.student_id
            WHERE sc.course_id = ?
            ORDER BY s.name
        """, (course_id,))

        return [dict(row) for row in c.fetchall()]

def update_lecture_status(lecture_id, is_active):
    """Update the active status of a lecture"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        try:
            if is_active:
                # If activating, try to set end_time to NULL
                try:
                    c.execute(
                        "UPDATE lectures SET is_active = 1, end_time = NULL WHERE id = ?",
                        (lecture_id,)
                    )
                except sqlite3.OperationalError:
                    # If end_time column doesn't exist, just update is_active
                    c.execute(
                        "UPDATE lectures SET is_active = 1 WHERE id = ?",
                        (lecture_id,)
                    )
            else:
                # If deactivating, try to set end_time to current time
                now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                try:
                    c.execute(
                        "UPDATE lectures SET is_active = 0, end_time = ? WHERE id = ?",
                        (now, lecture_id)
                    )
                except sqlite3.OperationalError:
                    # If end_time column doesn't exist, just update is_active
                    c.execute(
                        "UPDATE lectures SET is_active = 0 WHERE id = ?",
                        (lecture_id,)
                    )

            conn.commit()
            return c.rowcount > 0
        except Exception as e:
            print(f"Error updating lecture status: {e}")
            return False

def get_student_by_user_id(user_id):
    """Get student information by user ID"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT * FROM students WHERE user_id = ?", (user_id,))
        student = c.fetchone()
        if student:
            return dict(student)
        return None

def get_lecturer_by_user_id(user_id):
    """Get lecturer information by user ID"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("""
            SELECT l.id, l.name, l.staff_id, l.department, l.email, l.phone,
                   CASE WHEN l.face_encoding IS NOT NULL THEN 1 ELSE 0 END as has_face,
                   u.username
            FROM lecturers l
            JOIN users u ON l.user_id = u.id
            WHERE l.user_id = ?
        """, (user_id,))
        lecturer = c.fetchone()
        if lecturer:
            return dict(lecturer)
        return None

def get_admin_by_user_id(user_id):
    """Get admin information by user ID"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT * FROM admins WHERE user_id = ?", (user_id,))
        admin = c.fetchone()
        if admin:
            return dict(admin)
        return None

def update_student_profile(student_id, email, phone, email_notifications, sms_notifications):
    """Update student profile information"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Update student record
        c.execute("""
            UPDATE students
            SET email = ?, phone = ?, email_notifications = ?, sms_notifications = ?
            WHERE id = ?
        """, (email, phone, email_notifications, sms_notifications, student_id))

        conn.commit()
        return c.rowcount > 0

def update_lecturer_profile(lecturer_id, email, phone, email_notifications, sms_notifications):
    """Update lecturer profile information"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Update lecturer record
        c.execute("""
            UPDATE lecturers
            SET email = ?, phone = ?
            WHERE id = ?
        """, (email, phone, lecturer_id))

        # Get user_id for the lecturer
        c.execute("SELECT user_id FROM lecturers WHERE id = ?", (lecturer_id,))
        result = c.fetchone()
        if result and result[0]:
            user_id = result[0]

            # Update notification preferences
            c.execute("""
                INSERT OR REPLACE INTO user_preferences (user_id, setting_key, setting_value)
                VALUES (?, 'email_notifications', ?)
            """, (user_id, 'true' if email_notifications else 'false'))

            c.execute("""
                INSERT OR REPLACE INTO user_preferences (user_id, setting_key, setting_value)
                VALUES (?, 'sms_notifications', ?)
            """, (user_id, 'true' if sms_notifications else 'false'))

        conn.commit()
        return c.rowcount > 0

def update_admin_profile(admin_id, email, phone, email_notifications, sms_notifications):
    """Update admin profile information"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Update admin record
        c.execute("""
            UPDATE admins
            SET email = ?, phone = ?
            WHERE id = ?
        """, (email, phone, admin_id))

        # Get user_id for the admin
        c.execute("SELECT user_id FROM admins WHERE id = ?", (admin_id,))
        result = c.fetchone()
        if result and result[0]:
            user_id = result[0]

            # Update notification preferences
            c.execute("""
                INSERT OR REPLACE INTO user_preferences (user_id, setting_key, setting_value)
                VALUES (?, 'email_notifications', ?)
            """, (user_id, 'true' if email_notifications else 'false'))

            c.execute("""
                INSERT OR REPLACE INTO user_preferences (user_id, setting_key, setting_value)
                VALUES (?, 'sms_notifications', ?)
            """, (user_id, 'true' if sms_notifications else 'false'))

        conn.commit()
        return c.rowcount > 0

def update_user_preference(user_id, setting_key, setting_value):
    """Update or create a user preference setting"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        c.execute("""
            INSERT OR REPLACE INTO user_preferences (user_id, setting_key, setting_value)
            VALUES (?, ?, ?)
        """, (user_id, setting_key, setting_value))

        conn.commit()
        return True

def is_student_present(student_id, lecture_id):
    """Check if a student is already marked present for a lecture"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        c.execute("""
            SELECT COUNT(*) FROM attendance
            WHERE student_id = ? AND lecture_id = ?
        """, (student_id, lecture_id))
        count = c.fetchone()[0]
        return count > 0

def record_student_attendance(student_id, lecture_id, is_late=False, confidence=0.0):
    """Record student attendance for a lecture"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Check if attendance already recorded
        c.execute("""
            SELECT id FROM attendance
            WHERE student_id = ? AND lecture_id = ?
        """, (student_id, lecture_id))

        if c.fetchone():
            # Already recorded
            return None

        # Record attendance
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        c.execute("""
            INSERT INTO attendance (student_id, lecture_id, timestamp, is_late, confidence)
            VALUES (?, ?, ?, ?, ?)
        """, (student_id, lecture_id, now, 1 if is_late else 0, confidence))

        conn.commit()
        return c.lastrowid

def log_system_event(message, level="INFO"):
    """Log a system event"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        c.execute("""
            INSERT INTO system_logs (timestamp, level, message)
            VALUES (?, ?, ?)
        """, (now, level, message))

        conn.commit()
        return c.lastrowid

def fix_conflicting_settings():
    """Fix any system settings that conflict with Flask's built-in template variables"""
    try:
        with sqlite3.connect(DB_PATH) as conn:
            c = conn.cursor()

            # List of keys that might conflict with Flask's built-in template variables
            conflicting_keys = ['csrf_token', 'request', 'session', 'g', 'url_for']

            for key in conflicting_keys:
                c.execute("SELECT setting_value FROM system_settings WHERE setting_key = ?", (key,))
                result = c.fetchone()

                if result:
                    new_key = f"app_{key}"
                    print(f"Found conflicting setting '{key}', renaming to '{new_key}'")
                    c.execute("UPDATE system_settings SET setting_key = ? WHERE setting_key = ?", (new_key, key))
                    conn.commit()

        return True
    except Exception as e:
        print(f"Error fixing conflicting settings: {e}")
        return False

def calculate_attendance_percentage(attendance_data):
    """Calculate the attendance percentage based on attendance records"""
    if not attendance_data:
        return 0.0

    total_records = len(attendance_data)
    on_time_count = sum(1 for record in attendance_data if not record['is_late'])

    # Calculate percentage (on time + late = total attendance)
    attendance_percentage = (total_records / total_records) * 100 if total_records > 0 else 0

    return round(attendance_percentage, 1)

def get_all_departments():
    """Get all unique departments from students and lecturers"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        # Get unique departments from students
        c.execute("SELECT DISTINCT department FROM students WHERE department IS NOT NULL AND department != ''")
        student_departments = [row['department'] for row in c.fetchall()]

        # Get unique departments from lecturers
        c.execute("SELECT DISTINCT department FROM lecturers WHERE department IS NOT NULL AND department != ''")
        lecturer_departments = [row['department'] for row in c.fetchall()]

        # Combine and remove duplicates
        all_departments = list(set(student_departments + lecturer_departments))
        all_departments.sort()  # Sort alphabetically

        return all_departments

def add_end_time_column():
    """Add end_time column to lectures table if it doesn't exist"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Check if end_time column exists
        try:
            c.execute("SELECT end_time FROM lectures LIMIT 1")
            # If we get here, the column exists
            return True
        except sqlite3.OperationalError:
            # Column doesn't exist, add it
            try:
                c.execute("ALTER TABLE lectures ADD COLUMN end_time TIMESTAMP")
                conn.commit()
                print("Added end_time column to lectures table")
                return True
            except Exception as e:
                print(f"Error adding end_time column: {e}")
                return False

def ensure_lecturer_has_courses(lecturer_id):
    """Check if a lecturer has any assigned courses and add some if not"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        # Check if lecturer has any courses
        c.execute("SELECT COUNT(*) FROM lecturer_courses WHERE lecturer_id = ?", (lecturer_id,))
        count = c.fetchone()[0]

        if count == 0:
            # Get all courses
            c.execute("SELECT id FROM courses LIMIT 3")
            courses = c.fetchall()

            # Assign up to 3 courses to the lecturer
            for course in courses:
                try:
                    c.execute(
                        "INSERT INTO lecturer_courses (lecturer_id, course_id) VALUES (?, ?)",
                        (lecturer_id, course[0])
                    )
                except sqlite3.IntegrityError:
                    # Skip if already assigned
                    pass

            conn.commit()
            return True

        return False

def ensure_courses_exist():
    """Make sure there are courses in the database"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()

        # Check if there are any courses
        c.execute("SELECT COUNT(*) FROM courses")
        count = c.fetchone()[0]

        if count == 0:
            # Add some sample courses
            sample_courses = [
                ('CS101', 'Introduction to Computer Science', 'Computer Science'),
                ('CS201', 'Data Structures and Algorithms', 'Computer Science'),
                ('MATH101', 'Calculus I', 'Mathematics'),
                ('PHYS101', 'Physics I', 'Physics'),
                ('ENG101', 'English Composition', 'English')
            ]

            for code, title, department in sample_courses:
                try:
                    c.execute(
                        "INSERT INTO courses (code, title, department) VALUES (?, ?, ?)",
                        (code, title, department)
                    )
                except sqlite3.IntegrityError:
                    # Skip if already exists
                    pass

            conn.commit()
            return True

        return False
