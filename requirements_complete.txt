# Biometric Attendance System - Complete Requirements
# Core dependencies for the biometric attendance system

# Computer Vision and Face Recognition
face_recognition>=1.3.0
opencv-python>=4.5.0
numpy>=1.21.0
Pillow>=8.0.0

# Data Processing
pandas>=1.3.0

# Web Framework
Flask>=2.0.0
Flask-Login>=0.6.0
Flask-Session>=0.4.0
Werkzeug>=2.0.0

# System Monitoring
psutil>=5.8.0

# Additional utilities
click>=8.0.0
itsdangerous>=2.0.0
Jinja2>=3.0.0
MarkupSafe>=2.0.0
colorama>=0.4.0
six>=1.15.0
python-dateutil>=2.8.0

# Installation Notes:
# - Requires Python 3.10.x (newer versions may have compatibility issues)
# - Visual Studio C++ Build Tools required for some packages
# - Use: pip install -r requirements_complete.txt

# System Features:
# ✅ Multi-Modal Biometric System (RGB, IR, Depth)
# ✅ Advanced Anti-Spoofing Detection
# ✅ Face Recognition with Encoding Caching
# ✅ Real-time Attendance Monitoring
# ✅ Web Portal Interface
# ✅ Production Control Center GUI
