import { Typography, Box, Container, Grid, Button, Paper } from '@mui/material';
import Layout from '../components/layout/Layout';
import FeatureCard from '../components/ui/FeatureCard';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import MedicationIcon from '@mui/icons-material/Medication';
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import VolunteerActivismIcon from '@mui/icons-material/VolunteerActivism';
import { useNavigate } from 'react-router-dom';
import { useFirebase } from '../contexts/FirebaseContext';

function Home() {
  const navigate = useNavigate();
  const { currentUser } = useFirebase();

  return (
    <Layout>
      {/* Hero Section */}
      <Box 
        sx={{ 
          background: 'linear-gradient(135deg, #0072ff 0%, #0044b3 100%)',
          color: 'white',
          py: { xs: 6, md: 12 },
          mb: 8,
          position: 'relative',
          overflow: 'hidden',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: 0,
            width: '100%',
            height: '30%',
            background: 'linear-gradient(to top, rgba(0,0,0,0.1), rgba(0,0,0,0))',
            pointerEvents: 'none'
          }
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Box>
                <Typography 
                  variant="h2" 
                  component="h1" 
                  sx={{ 
                    fontWeight: 'bold',
                    mb: 2,
                    fontSize: { xs: '2.5rem', md: '3.5rem' },
                    lineHeight: 1.2
                  }}
                >
                  Modern Healthcare Solutions
                </Typography>
                <Typography 
                  variant="h6" 
                  sx={{ 
                    mb: 4,
                    opacity: 0.9,
                    fontWeight: 400,
                    lineHeight: 1.6
                  }}
                >
                  Experience the future of healthcare with our innovative platform designed for patients and providers.
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Button 
                    variant="contained" 
                    color="secondary"
                    size="large"
                    sx={{ 
                      px: 4, 
                      py: 1.5,
                      fontSize: '1rem',
                      bgcolor: 'white',
                      color: 'primary.main',
                      '&:hover': {
                        bgcolor: 'rgba(255,255,255,0.9)'
                      }
                    }}
                    onClick={() => currentUser ? navigate('/dashboard') : navigate('/register')}
                  >
                    Get Started
                  </Button>
                  <Button 
                    variant="outlined" 
                    color="inherit" 
                    size="large"
                    sx={{ 
                      borderColor: 'white',
                      px: 4,
                      py: 1.5,
                      fontSize: '1rem',
                      '&:hover': {
                        borderColor: 'white',
                        bgcolor: 'rgba(255,255,255,0.1)'
                      }
                    }}
                    onClick={() => navigate('/login')}
                  >
                    Sign In
                  </Button>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box 
                sx={{ 
                  height: 300,
                  backgroundColor: 'rgba(255,255,255,0.1)',
                  borderRadius: 4,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                {/* Loading placeholder */}
                <Box 
                  sx={{ 
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 1,
                    transition: 'opacity 0.3s ease',
                    '&.loaded': {
                      opacity: 0,
                      zIndex: -1
                    }
                  }}
                  className="video-placeholder"
                >
                  <HealthAndSafetyIcon sx={{ fontSize: 120, opacity: 0.8 }} />
                </Box>
                
                {/* YouTube iframe */}
                <Box 
                  component="iframe"
                  src="https://www.youtube.com/embed/jG1VNSCsP5Q?autoplay=0"
                  title="Healthcare Platform Introduction"
                  frameBorder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    border: 'none'
                  }}
                  onLoad={(e) => {
                    const placeholder = document.querySelector('.video-placeholder');
                    if (placeholder) placeholder.classList.add('loaded');
                  }}
                />
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ mb: 8 }}>
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography 
            variant="h3" 
            component="h2" 
            sx={{ 
              fontWeight: 'bold',
              mb: 2 
            }}
          >
            Our Features
          </Typography>
          <Typography 
            variant="h6" 
            color="text.secondary"
            sx={{ 
              maxWidth: 700,
              mx: 'auto'
            }}
          >
            Discover how our platform can transform your healthcare experience with these powerful features.
          </Typography>
        </Box>

        <Grid container spacing={4}>
          <Grid item xs={12} sm={6} md={3}>
            <FeatureCard 
              icon={<CalendarMonthIcon fontSize="large" />}
              title="Appointment Scheduling"
              description="Book and manage appointments with healthcare providers easily."
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FeatureCard 
              icon={<MedicationIcon fontSize="large" />}
              title="Medication Tracking"
              description="Keep track of your medications and receive timely reminders."
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FeatureCard 
              icon={<MonitorHeartIcon fontSize="large" />}
              title="Health Monitoring"
              description="Monitor your vital signs and health metrics over time."
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FeatureCard 
              icon={<MedicalInformationIcon fontSize="large" />}
              title="Medical Records"
              description="Access your medical history and share it with providers securely."
            />
          </Grid>
        </Grid>
      </Container>

      {/* How It Works Section */}
      <Box sx={{ bgcolor: 'grey.50', py: 8, mb: 8 }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography 
              variant="h3" 
              component="h2" 
              sx={{ 
                fontWeight: 'bold',
                mb: 2 
              }}
            >
              How It Works
            </Typography>
            <Typography 
              variant="h6" 
              color="text.secondary"
              sx={{ 
                maxWidth: 700,
                mx: 'auto'
              }}
            >
              Our platform is designed to be simple and intuitive. Here's how you can get started.
            </Typography>
          </Box>

          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Box sx={{ p: 2 }}>
                <Box 
                  sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    mb: 4 
                  }}
                >
                  <Box 
                    sx={{ 
                      width: 50, 
                      height: 50, 
                      borderRadius: '50%', 
                      bgcolor: 'primary.main', 
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontWeight: 'bold',
                      fontSize: '1.25rem',
                      mr: 2
                    }}
                  >
                    1
                  </Box>
                  <Box>
                    <Typography variant="h5" fontWeight="bold" gutterBottom>
                      Create an Account
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      Sign up as a student or healthcare provider to access our platform.
                    </Typography>
                  </Box>
                </Box>
                
                <Box 
                  sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    mb: 4 
                  }}
                >
                  <Box 
                    sx={{ 
                      width: 50, 
                      height: 50, 
                      borderRadius: '50%', 
                      bgcolor: 'primary.main', 
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontWeight: 'bold',
                      fontSize: '1.25rem',
                      mr: 2
                    }}
                  >
                    2
                  </Box>
                  <Box>
                    <Typography variant="h5" fontWeight="bold" gutterBottom>
                      Complete Your Profile
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      Add your personal information and medical history to get personalized care.
                    </Typography>
                  </Box>
                </Box>
                
                <Box 
                  sx={{ 
                    display: 'flex', 
                    alignItems: 'center' 
                  }}
                >
                  <Box 
                    sx={{ 
                      width: 50, 
                      height: 50, 
                      borderRadius: '50%', 
                      bgcolor: 'primary.main', 
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontWeight: 'bold',
                      fontSize: '1.25rem',
                      mr: 2
                    }}
                  >
                    3
                  </Box>
                  <Box>
                    <Typography variant="h5" fontWeight="bold" gutterBottom>
                      Start Using the Platform
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      Book appointments, track medications, monitor your health, and more.
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Box 
                sx={{ 
                  height: 300,
                  bgcolor: 'white',
                  borderRadius: 4,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                  overflow: 'hidden'
                }}
              >
                <Box
                  component="img"
                  src="/yourhealthmatters.gif"
                  alt="Your Health Matters"
                  sx={{
                    maxWidth: '100%',
                    maxHeight: '100%',
                    objectFit: 'contain',
                    position: 'relative',
                    zIndex: 1
                  }}
                />
                
                {/* White shape overlay to cover bottom corner */}
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: -20,
                    right: -20,
                    width: 100,
                    height: 100,
                    backgroundColor: 'white',
                    borderRadius: '50%',
                    zIndex: 2,
                    boxShadow: '0 0 20px 20px white'
                  }}
                />
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* CTA Section */}
      <Container maxWidth="lg" sx={{ mb: 8 }}>
        <Paper 
          elevation={0} 
          sx={{ 
            p: { xs: 4, md: 6 }, 
            borderRadius: 4,
            background: 'linear-gradient(135deg, #0072ff 0%, #0044b3 100%)',
            color: 'white',
            textAlign: 'center',
            boxShadow: '0 10px 40px rgba(0,0,0,0.2)'
          }}
        >
          <VolunteerActivismIcon sx={{ fontSize: 60, mb: 2, opacity: 0.9 }} />
          <Typography variant="h3" component="h2" fontWeight="bold" gutterBottom>
            Ready to Get Started?
          </Typography>
          <Typography variant="h6" sx={{ mb: 4, opacity: 0.9, maxWidth: 800, mx: 'auto' }}>
            Join thousands of students and healthcare providers who are already using our platform.
            Experience the future of healthcare management today.
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
            <Button 
              variant="contained" 
              color="secondary"
              size="large"
              sx={{ 
                px: 4, 
                py: 1.5,
                fontSize: '1rem',
                bgcolor: 'white',
                color: 'primary.main',
                '&:hover': {
                  bgcolor: 'rgba(255,255,255,0.9)'
                }
              }}
              onClick={() => currentUser ? navigate('/dashboard') : navigate('/register')}
            >
              Get Started
            </Button>
            <Button 
              variant="outlined" 
              color="inherit" 
              size="large"
              sx={{ 
                borderColor: 'white',
                px: 4,
                py: 1.5,
                fontSize: '1rem',
                '&:hover': {
                  borderColor: 'white',
                  bgcolor: 'rgba(255,255,255,0.1)'
                }
              }}
              onClick={() => navigate('/login')}
            >
              Learn More
            </Button>
          </Box>
        </Paper>
      </Container>
    </Layout>
  );
}

export default Home;





