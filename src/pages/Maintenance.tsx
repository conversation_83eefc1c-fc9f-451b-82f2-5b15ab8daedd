import React from 'react';
import {
  Container,
  Box,
  Typography,
  Paper,
  Button,
  Alert
} from '@mui/material';
import ConstructionIcon from '@mui/icons-material/Construction';
import RefreshIcon from '@mui/icons-material/Refresh';
import HomeIcon from '@mui/icons-material/Home';

interface MaintenanceProps {
  message?: string;
}

const Maintenance: React.FC<MaintenanceProps> = ({
  message = 'System is currently under maintenance. Please try again later.'
}) => {
  const handleRefresh = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    // Use window.location instead of navigate since we're outside Router context
    window.location.href = '/';
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
        py: 4
      }}
    >
      <Container maxWidth="md">
        <Paper
          elevation={0}
          sx={{
            p: 6,
            textAlign: 'center',
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <ConstructionIcon
            sx={{
              fontSize: 80,
              color: 'warning.main',
              mb: 3
            }}
          />
          
          <Typography
            variant="h3"
            component="h1"
            fontWeight="bold"
            gutterBottom
            color="text.primary"
          >
            Under Maintenance
          </Typography>
          
          <Typography
            variant="h6"
            color="text.secondary"
            sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}
          >
            {message}
          </Typography>

          <Alert 
            severity="info" 
            sx={{ 
              mb: 4, 
              textAlign: 'left',
              borderRadius: 2 
            }}
          >
            <Typography variant="body2">
              <strong>What's happening?</strong><br />
              Our team is working to improve your experience. We'll be back online shortly.
            </Typography>
          </Alert>

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={handleRefresh}
              sx={{ borderRadius: 2 }}
            >
              Refresh Page
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<HomeIcon />}
              onClick={handleGoHome}
              sx={{ borderRadius: 2 }}
            >
              Go to Homepage
            </Button>
          </Box>

          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ mt: 4 }}
          >
            Thank you for your patience.
          </Typography>
        </Paper>
      </Container>
    </Box>
  );
};

export default Maintenance;
