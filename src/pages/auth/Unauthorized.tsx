import React from 'react';
import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, Container, Al<PERSON> } from '@mui/material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useFirebase } from '../../contexts/FirebaseContext';
import Header from '../../components/layout/Header';
import Footer from '../../components/layout/Footer';

const Unauthorized: React.FC = () => {
  const navigate = useNavigate();
  const { userProfile } = useAuth();
  const { logout } = useFirebase();

  const handleGoToDashboard = () => {
    // Redirect based on user role
    switch (userProfile?.role) {
      case 'admin':
        navigate('/admin/dashboard');
        break;
      case 'doctor':
        navigate('/doctor/dashboard');
        break;
      case 'student':
        navigate('/dashboard');
        break;
      default:
        navigate('/');
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <Header />
      <Container maxWidth="md" sx={{ flex: '1 0 auto' }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            py: 8,
            textAlign: 'center',
          }}
        >
          <Typography variant="h1" component="h1" sx={{ fontSize: '6rem', fontWeight: 700, color: 'error.main' }}>
            403
          </Typography>
          <Typography variant="h4" component="h2" sx={{ mb: 2 }}>
            Access Denied
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 500 }}>
            You don't have permission to access this page. This area is restricted to authorized users only.
          </Typography>

          {userProfile && (
            <Alert severity="info" sx={{ mb: 4, maxWidth: 500 }}>
              You are currently logged in as: <strong>{userProfile.email}</strong> with role: <strong>{userProfile.role}</strong>
            </Alert>
          )}

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
            <Button
              onClick={handleGoToDashboard}
              variant="contained"
              color="primary"
              size="large"
              sx={{ px: 4 }}
            >
              Go to My Dashboard
            </Button>
            <Button
              component={RouterLink}
              to="/"
              variant="outlined"
              color="primary"
              size="large"
              sx={{ px: 4 }}
            >
              Back to Home
            </Button>
            <Button
              onClick={handleLogout}
              variant="text"
              color="secondary"
              size="large"
              sx={{ px: 4 }}
            >
              Switch Account
            </Button>
          </Box>
        </Box>
      </Container>
      <Footer />
    </Box>
  );
};

export default Unauthorized;
