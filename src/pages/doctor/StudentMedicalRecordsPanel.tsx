import React, { useEffect, useState } from 'react';
import { Box, Typography, CircularProgress, Table, TableBody, TableCell, TableHead, TableRow, Paper, Button, Dialog, DialogTitle, DialogContent, DialogActions, TextField, LinearProgress } from '@mui/material';
import { getMedicalRecordsByStudent, createMedicalRecord } from '../../services/medicalRecordService';
import { useSupabaseFileUpload } from '../../hooks/useSupabaseFileUpload';
import type { MedicalRecord } from '../../services/medicalRecordService';

const StudentMedicalRecordsPanel: React.FC<{ student: { uid: string; name: string } }> = ({ student }) => {
  const [records, setRecords] = useState<MedicalRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [notes, setNotes] = useState('');
  const { uploadState, uploadMedia, resetUploadState } = useSupabaseFileUpload();

  useEffect(() => {
    setLoading(true);
    getMedicalRecordsByStudent(student.uid).then((data) => {
      setRecords(data);
      setLoading(false);
    });
  }, [student]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
      resetUploadState();
    }
  };

  const handleUpload = async () => {
    if (selectedFile) {
      await uploadMedia(student.uid, selectedFile, 'MEDICAL_RECORDS');
    }
  };

  const handleSaveRecord = async () => {
    await handleUpload();
    if (uploadState.fileUrl && selectedFile) {
      await createMedicalRecord({
        studentId: student.uid,
        studentName: student.name,
        doctorId: '', // Fill with current doctor ID if available
        doctorName: '', // Fill with current doctor name if available
        date: new Date().toISOString().split('T')[0],
        diagnosis: notes,
        symptoms: [],
        treatment: '',
        medications: [],
        notes,
        followUpRequired: false,
        followUpDate: undefined,
        status: 'active',
        // File info can be included in notes or a custom field if needed
      });
    }
    setUploadDialogOpen(false);
    setSelectedFile(null);
    setNotes('');
    resetUploadState();
    // Optionally refresh records
    getMedicalRecordsByStudent(student.uid).then((data) => setRecords(data));
  };

  if (loading) return <CircularProgress />;

  return (
    <Paper sx={{ p: 2 }}>
      <Button variant="contained" onClick={() => setUploadDialogOpen(true)} sx={{ mb: 2 }}>
        Upload Medical Record
      </Button>
      <Typography variant="h6">Medical Records for {student.name}</Typography>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Diagnosis</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Date</TableCell>
            <TableCell>Created At</TableCell>
            <TableCell>Notes</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {records.map((r) => (
            <TableRow key={r.id}>
              <TableCell>{r.diagnosis}</TableCell>
              <TableCell>{r.status}</TableCell>
              <TableCell>{r.date}</TableCell>
              <TableCell>{r.createdAt ? new Date(r.createdAt).toLocaleDateString() : ''}</TableCell>
              <TableCell>
                {r.notes && <span>{r.notes}</span>}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)}>
        <DialogTitle>Upload Medical Record</DialogTitle>
        <DialogContent>
          <input type="file" onChange={handleFileChange} />
          <TextField
            label="Notes"
            value={notes}
            onChange={e => setNotes(e.target.value)}
            fullWidth
            multiline
            sx={{ mt: 2 }}
          />
          {uploadState.isUploading && <LinearProgress variant="determinate" value={uploadState.progress} sx={{ mt: 2 }} />}
          {uploadState.error && <div style={{ color: 'red' }}>{uploadState.error}</div>}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveRecord} disabled={uploadState.isUploading || !selectedFile}>Save</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default StudentMedicalRecordsPanel;
