import React, { useState, useEffect } from 'react';
import { 
  Container, Typography, Box, Paper, Button, Grid, 
  Table, TableBody, TableCell, TableContainer, TableHead, 
  TableRow, IconButton, Chip, TextField, InputAdornment,
  Tabs, Tab, Dialog, DialogTitle, DialogContent, DialogActions,
  DialogContentText
} from '@mui/material';
import { 
  Add as AddIcon, 
  Edit as EditIcon, 
  Delete as DeleteIcon,
  Search as SearchIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';

const HealthResourcesManagement = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [resources, setResources] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [resourceToDelete, setResourceToDelete] = useState(null);
  
  useEffect(() => {
    // Mock data fetch
    setTimeout(() => {
      const mockResources = [
        { 
          id: 1, 
          title: 'Managing Exam Stress', 
          category: 'Mental Health', 
          publishDate: '2023-05-05',
          status: 'published',
          views: 245
        },
        { 
          id: 2, 
          title: 'Nutrition Tips for Students', 
          category: 'Nutrition', 
          publishDate: '2023-05-03',
          status: 'published',
          views: 189
        },
        { 
          id: 3, 
          title: 'Importance of Sleep', 
          category: 'Wellness', 
          publishDate: '2023-04-28',
          status: 'published',
          views: 312
        },
        { 
          id: 4, 
          title: 'Seasonal Allergies Guide', 
          category: 'Health', 
          publishDate: '2023-04-15',
          status: 'draft',
          views: 0
        },
        { 
          id: 5, 
          title: 'Mental Health First Aid', 
          category: 'Mental Health', 
          publishDate: null,
          status: 'draft',
          views: 0
        }
      ];
      
      setResources(mockResources);
      setLoading(false);
    }, 1000);
  }, []);
  
  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };
  
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  const openDeleteDialog = (resource) => {
    setResourceToDelete(resource);
    setDeleteDialogOpen(true);
  };
  
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setResourceToDelete(null);
  };
  
  const handleDeleteResource = () => {
    // In a real app, you would call an API to delete the resource
    setResources(resources.filter(r => r.id !== resourceToDelete.id));
    closeDeleteDialog();
  };
  
  const handlePublishResource = (resourceId) => {
    // In a real app, you would call an API to update the resource status
    setResources(resources.map(resource => 
      resource.id === resourceId 
        ? { ...resource, status: 'published', publishDate: new Date().toISOString().split('T')[0] } 
        : resource
    ));
  };
  
  const filteredResources = resources.filter(resource => {
    // Filter by search term
    const matchesSearch = 
      resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.category.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Filter by status tab
    const matchesStatus = 
      (activeTab === 0) || // All
      (activeTab === 1 && resource.status === 'published') || // Published
      (activeTab === 2 && resource.status === 'draft'); // Drafts
    
    return matchesSearch && matchesStatus;
  });
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            My Health Resources
          </Typography>
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => navigate('/doctor/health-resources/create')}
            sx={{ 
              borderRadius: 2,
              px: 3
            }}
          >
            Create New Resource
          </Button>
        </Box>
        
        {/* Search and Filters */}
        <Paper 
          sx={{ 
            p: 3, 
            mb: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search resources..."
                value={searchTerm}
                onChange={handleSearch}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ borderRadius: 2 }}
              />
            </Grid>
          </Grid>
        </Paper>
        
        {/* Status Tabs */}
        <Paper 
          sx={{ 
            mb: 3, 
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              px: 2,
              '& .MuiTab-root': {
                py: 2
              }
            }}
          >
            <Tab label="All Resources" />
            <Tab label="Published" />
            <Tab label="Drafts" />
          </Tabs>
        </Paper>
        
        {/* Resources Table */}
        <Paper 
          sx={{ 
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <TableContainer>
            <Table>
              <TableHead sx={{ bgcolor: 'background.paper' }}>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Title</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Category</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Views</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }} align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                      <Typography>Loading resources...</Typography>
                    </TableCell>
                  </TableRow>
                ) : filteredResources.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                      <Typography>No resources found</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredResources.map((resource) => (
                    <TableRow key={resource.id} hover>
                      <TableCell>
                        <Typography fontWeight="medium">{resource.title}</Typography>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={resource.category} 
                          size="small" 
                          color="primary" 
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={resource.status === 'published' ? 'Published' : 'Draft'} 
                          size="small"
                          color={resource.status === 'published' ? 'success' : 'default'}
                        />
                      </TableCell>
                      <TableCell>
                        {resource.publishDate || '—'}
                      </TableCell>
                      <TableCell>
                        {resource.views}
                      </TableCell>
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                          <IconButton 
                            size="small" 
                            color="primary"
                            onClick={() => navigate(`/health-resources/${resource.id}`)}
                            sx={{ mr: 1 }}
                          >
                            <ViewIcon fontSize="small" />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            color="primary"
                            onClick={() => navigate(`/doctor/health-resources/edit/${resource.id}`)}
                            sx={{ mr: 1 }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            color="error"
                            onClick={() => openDeleteDialog(resource)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Box>
                        {resource.status === 'draft' && (
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={() => handlePublishResource(resource.id)}
                            sx={{ mt: 1 }}
                          >
                            Publish
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
        
        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={closeDeleteDialog}
        >
          <DialogTitle>Delete Resource</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete "{resourceToDelete?.title}"? This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={closeDeleteDialog}>Cancel</Button>
            <Button onClick={handleDeleteResource} color="error" variant="contained">
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Layout>
  );
};

export default HealthResourcesManagement;
