import React, { useEffect, useState } from 'react';
import { Box, Typography, CircularProgress, Table, TableBody, TableCell, TableHead, TableRow, Paper } from '@mui/material';
import { getHealthMetrics } from '../../features/healthMetrics/healthMetricsService';
import type { HealthMetric } from '../../features/healthMetrics/healthMetricsService';

const StudentMetricsPanel: React.FC<{ student: { uid: string; name: string } }> = ({ student }) => {
  const [metrics, setMetrics] = useState<HealthMetric[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(true);
    getHealthMetrics(student.uid).then((data) => {
      setMetrics(data);
      setLoading(false);
    });
  }, [student]);

  if (loading) return <CircularProgress />;

  return (
    <Paper sx={{ p: 2 }}>
      <Typography variant="h6">Health Metrics for {student.name}</Typography>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Type</TableCell>
            <TableCell>Value</TableCell>
            <TableCell>Date</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {metrics.map((m) => (
            <TableRow key={m.id}>
              <TableCell>{m.type}</TableCell>
              <TableCell>{m.value || `${m.systolic || ''}/${m.diastolic || ''}` || m.steps || m.calories}</TableCell>
              <TableCell>{m.date}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Paper>
  );
};

export default StudentMetricsPanel;
