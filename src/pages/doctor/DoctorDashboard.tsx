import React, { useState, useEffect } from 'react';
import {
  Container, Typography, Box, Grid, Paper, Button,
  List, ListItem, ListItemIcon, ListItemText, Avatar,
  Card, CardContent, Chip, Badge, IconButton, Divider,
  LinearProgress, Alert
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArticleIcon from '@mui/icons-material/Article';
import AddIcon from '@mui/icons-material/Add';
import PeopleIcon from '@mui/icons-material/People';
import EventIcon from '@mui/icons-material/Event';
import ChatIcon from '@mui/icons-material/Chat';
import NotificationsIcon from '@mui/icons-material/Notifications';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import PersonIcon from '@mui/icons-material/Person';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { useFirebase } from '../../contexts/FirebaseContext';
import DoctorProfileCompletionOverlay from '../../components/DoctorProfileCompletionOverlay';
import { useAuth } from '../../contexts/AuthContext';
import { getDoctorStudents } from '../../services/doctorStudentService';
import { getAppointmentsByDoctor } from '../../services/appointmentService';
import { chatService } from '../../services/chatService';
import { getHealthResourcesByAuthor } from '../../services/healthResourcesService';

const DoctorDashboard = () => {
  const navigate = useNavigate();
  const { currentUser } = useFirebase();
  const { userProfile } = useAuth();
  const [loading, setLoading] = useState(true);
  const [showProfileOverlay, setShowProfileOverlay] = useState(false);

  useEffect(() => {
    // Show overlay if doctor profile is not complete
    if (userProfile && userProfile.role === 'doctor' && !userProfile.profileCompleted) {
      setShowProfileOverlay(true);
    } else {
      setShowProfileOverlay(false);
    }
  }, [userProfile]);

  // Real doctor data from Firebase
  const [doctorStats, setDoctorStats] = useState({
    totalPatients: 0,
    todayAppointments: 0,
    pendingChats: 0,
    completedToday: 0
  });

  // Real appointments from Firebase
  const [todayAppointments, setTodayAppointments] = useState([]);

  // Real chats from Firebase
  const [pendingChats, setPendingChats] = useState([]);

  // Real health resources from Firebase
  const [healthResources, setHealthResources] = useState([]);

  // Load real data from Firebase
  useEffect(() => {
    const loadDoctorData = async () => {
      if (!currentUser?.uid) return;

      try {
        setLoading(true);

        // Load doctor's students
        const students = await getDoctorStudents(currentUser.uid);

        // Load doctor's appointments for today
        const appointments = await getAppointmentsByDoctor(currentUser.uid);
        const today = new Date().toDateString();
        const todayAppts = appointments.filter(apt =>
          new Date(apt.date).toDateString() === today
        );

        // Load doctor's health resources
        const resources = await getHealthResourcesByAuthor(currentUser.uid);
        const recentResources = resources.slice(0, 3).map(resource => ({
          id: resource.id,
          title: resource.title,
          category: resource.category,
          status: resource.status,
          date: resource.updatedAt?.toDate?.()?.toLocaleDateString() || 'N/A'
        }));
        setHealthResources(recentResources);

        // Subscribe to conversations for pending chats
        const unsubscribe = chatService.subscribeToConversations(currentUser.uid, (conversations) => {
          const chatsWithUnread = conversations.filter(conv =>
            (conv.unreadCounts?.[currentUser.uid] || 0) > 0
          );

          const chatList = chatsWithUnread.map(conv => {
            const otherParticipantId = conv.participants.find(id => id !== currentUser.uid);
            const participantDetails = conv.participantDetails[otherParticipantId];

            return {
              id: conv.id,
              patient: participantDetails?.name || 'Unknown Patient',
              lastMessage: conv.lastMessage?.content || '',
              time: conv.lastMessage?.timestamp?.toDate?.()?.toLocaleTimeString() || '',
              unread: conv.unreadCounts?.[currentUser.uid] || 0
            };
          });

          setPendingChats(chatList);
        });

        // Update stats
        setDoctorStats({
          totalPatients: students.length,
          todayAppointments: todayAppts.length,
          pendingChats: 0, // Will be updated by chat subscription
          completedToday: todayAppts.filter(apt => apt.status === 'completed').length
        });

        // Update appointments
        setTodayAppointments(todayAppts.map(apt => ({
          id: apt.id,
          patient: apt.studentName || 'Unknown Patient',
          time: new Date(apt.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          type: apt.type || 'Consultation',
          status: apt.status || 'upcoming'
        })));

        setLoading(false);

        // Return cleanup function
        return () => {
          if (unsubscribe) unsubscribe();
        };

      } catch (error) {
        console.error('Error loading doctor data:', error);
        setLoading(false);
      }
    };

    loadDoctorData();
  }, [currentUser?.uid]);



  return (
    <>
      <DoctorProfileCompletionOverlay open={showProfileOverlay} onComplete={() => setShowProfileOverlay(false)} />
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          {/* Dashboard Header */}
          <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
                Doctor Dashboard
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Welcome back, Dr. {currentUser?.displayName || 'User'}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <IconButton
                color="primary"
                onClick={() => navigate('/doctor/chat')}
                sx={{ position: 'relative' }}
              >
                <Badge badgeContent={doctorStats.pendingChats} color="error">
                  <ChatIcon />
                </Badge>
              </IconButton>
              <IconButton color="primary">
                <Badge badgeContent={5} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Box>
          </Box>

          {/* Stats Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h4" fontWeight="bold" color="primary.main">
                        {doctorStats.totalPatients}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Patients
                      </Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: 'primary.light', color: 'primary.main' }}>
                      <PeopleIcon />
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h4" fontWeight="bold" color="success.main">
                        {doctorStats.todayAppointments}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Today's Appointments
                      </Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: 'success.light', color: 'success.main' }}>
                      <EventIcon />
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h4" fontWeight="bold" color="warning.main">
                        {doctorStats.pendingChats}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Pending Chats
                      </Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: 'warning.light', color: 'warning.main' }}>
                      <ChatIcon />
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h4" fontWeight="bold" color="info.main">
                        {doctorStats.completedToday}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Completed Today
                      </Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: 'info.light', color: 'info.main' }}>
                      <TrendingUpIcon />
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Quick Actions */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Quick Actions
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<PeopleIcon />}
                      onClick={() => navigate('/doctor/patients')}
                      sx={{ borderRadius: 2, py: 1.5 }}
                    >
                      View Patients
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<EventIcon />}
                      onClick={() => navigate('/doctor/appointments')}
                      sx={{ borderRadius: 2, py: 1.5 }}
                    >
                      Appointments
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<ChatIcon />}
                      onClick={() => navigate('/doctor/chat')}
                      sx={{ borderRadius: 2, py: 1.5 }}
                    >
                      Chat Center
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<PersonIcon />}
                      onClick={() => navigate('/doctor/profile')}
                      sx={{ borderRadius: 2, py: 1.5 }}
                    >
                      My Profile
                    </Button>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" fontWeight="bold">
                    Today's Schedule
                  </Typography>
                  <Button
                    size="small"
                    onClick={() => navigate('/doctor/appointments')}
                    sx={{ borderRadius: 2 }}
                  >
                    View All
                  </Button>
                </Box>
                <List sx={{ minHeight: 200, maxHeight: 300, overflow: 'auto' }}>
                  {todayAppointments.slice(0, 4).map((appointment) => (
                    <ListItem key={appointment.id} sx={{ px: 0, py: 1 }}>
                      <ListItemIcon>
                        <FiberManualRecordIcon
                          sx={{
                            color: appointment.status === 'completed' ? 'success.main' : 'primary.main',
                            fontSize: 12
                          }}
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="body2" fontWeight="medium">
                              {appointment.patient}
                            </Typography>
                            <Chip
                              label={appointment.status}
                              size="small"
                              color={appointment.status === 'completed' ? 'success' : 'primary'}
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={`${appointment.time} - ${appointment.type}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Paper>
            </Grid>
          </Grid>

          {/* Pending Chats & Health Resources */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" fontWeight="bold">
                    Pending Chats
                  </Typography>
                  <Button
                    size="small"
                    onClick={() => navigate('/doctor/chat')}
                    sx={{ borderRadius: 2 }}
                  >
                    View All
                  </Button>
                </Box>
                <List sx={{ minHeight: 200, maxHeight: 300, overflow: 'auto' }}>
                  {pendingChats.map((chat) => (
                    <ListItem
                      key={chat.id}
                      sx={{
                        px: 0,
                        py: 1,
                        cursor: 'pointer',
                        '&:hover': { bgcolor: 'action.hover' },
                        borderRadius: 1
                      }}
                      onClick={() => navigate('/doctor/chat')}
                    >
                      <ListItemIcon>
                        <Avatar sx={{ width: 40, height: 40, bgcolor: 'primary.light' }}>
                          {chat.patient.split(' ').map(n => n[0]).join('')}
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="body2" fontWeight="medium">
                              {chat.patient}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Badge badgeContent={chat.unread} color="error" />
                              <Typography variant="caption" color="text.secondary"
                              sx={{
                                pl: 1
                              }}
                              >
                                {chat.time}
                              </Typography>
                            </Box>
                          </Box>
                        }
                        secondary={
                          <Typography variant="body2" color="text.secondary" noWrap>
                            {chat.lastMessage}
                          </Typography>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" fontWeight="bold">
                    Health Resources
                  </Typography>
                  <Button
                    size="small"
                    onClick={() => navigate('/doctor/health-resources')}
                    sx={{ borderRadius: 2 }}
                  >
                    Manage All
                  </Button>
                </Box>
                <List sx={{ minHeight: 200, maxHeight: 300, overflow: 'auto' }}>
                  {healthResources.length > 0 ? healthResources.map((resource) => (
                    <ListItem key={resource.id} sx={{ px: 0, py: 1 }}>
                      <ListItemIcon>
                        <ArticleIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="body2" fontWeight="medium">
                              {resource.title}
                            </Typography>
                            <Chip
                              label={resource.status}
                              size="small"
                              color={resource.status === 'published' ? 'success' : 'warning'}
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={`${resource.category} • ${resource.date}`}
                      />
                    </ListItem>
                  )) : (
                    <ListItem>
                      <ListItemText
                        primary="No health resources yet"
                        secondary="Create your first health resource to help students"
                        sx={{ textAlign: 'center', py: 2 }}
                      />
                    </ListItem>
                  )}
                </List>
                <Divider sx={{ my: 2 }} />
                <Button
                  fullWidth
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => navigate('/doctor/health-resources/new')}
                  sx={{ borderRadius: 2 }}
                >
                  Create New Resource
                </Button>
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Layout>
    </>
  );
};

export default DoctorDashboard;


