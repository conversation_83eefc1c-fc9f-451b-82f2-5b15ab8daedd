import React, { useState, useEffect } from 'react';
import { 
  Container, Typography, Box, Grid, Paper, Button,
  List, ListItem, ListItemText, Avatar, Divider,
  TextField, InputAdornment, Chip, Card, CardContent,
  IconButton, Badge, Dialog, DialogTitle, DialogContent,
  DialogActions, Tab, Tabs
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import SearchIcon from '@mui/icons-material/Search';
import ChatIcon from '@mui/icons-material/Chat';
import VideoCallIcon from '@mui/icons-material/VideoCall';
import PhoneIcon from '@mui/icons-material/Phone';
import PersonIcon from '@mui/icons-material/Person';
import EventIcon from '@mui/icons-material/Event';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import FilterListIcon from '@mui/icons-material/FilterList';
import { useFirebase } from '../../contexts/FirebaseContext';

const DoctorPatients = () => {
  const navigate = useNavigate();
  const { currentUser } = useFirebase();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  // Mock patient data
  const [patients, setPatients] = useState([
    {
      id: 1,
      name: 'John Smith',
      age: 22,
      gender: 'Male',
      studentId: 'ST001',
      department: 'Computer Science',
      year: '3rd Year',
      lastVisit: '2024-01-15',
      status: 'Active',
      avatar: '',
      contactInfo: {
        email: '<EMAIL>',
        phone: '+****************'
      },
      medicalInfo: {
        bloodType: 'O+',
        allergies: ['Peanuts', 'Shellfish'],
        conditions: ['Asthma'],
        medications: ['Albuterol Inhaler']
      },
      recentActivity: [
        { date: '2024-01-15', type: 'Consultation', notes: 'Routine check-up' },
        { date: '2024-01-10', type: 'Chat', notes: 'Discussed medication side effects' }
      ]
    },
    {
      id: 2,
      name: 'Emma Davis',
      age: 20,
      gender: 'Female',
      studentId: 'ST002',
      department: 'Biology',
      year: '2nd Year',
      lastVisit: '2024-01-12',
      status: 'Active',
      avatar: '',
      contactInfo: {
        email: '<EMAIL>',
        phone: '+****************'
      },
      medicalInfo: {
        bloodType: 'A+',
        allergies: ['None'],
        conditions: ['Anxiety'],
        medications: ['Sertraline 50mg']
      },
      recentActivity: [
        { date: '2024-01-12', type: 'Follow-up', notes: 'Anxiety management progress' },
        { date: '2024-01-08', type: 'Video Call', notes: 'Therapy session' }
      ]
    },
    {
      id: 3,
      name: 'Alex Johnson',
      age: 21,
      gender: 'Male',
      studentId: 'ST003',
      department: 'Engineering',
      year: '3rd Year',
      lastVisit: '2024-01-10',
      status: 'Active',
      avatar: '',
      contactInfo: {
        email: '<EMAIL>',
        phone: '+****************'
      },
      medicalInfo: {
        bloodType: 'B+',
        allergies: ['Latex'],
        conditions: ['Migraine'],
        medications: ['Sumatriptan']
      },
      recentActivity: [
        { date: '2024-01-10', type: 'Consultation', notes: 'Migraine treatment review' },
        { date: '2024-01-05', type: 'Chat', notes: 'Medication adjustment' }
      ]
    }
  ]);

  const filteredPatients = patients.filter(patient =>
    patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handlePatientClick = (patient) => {
    setSelectedPatient(patient);
    setDialogOpen(true);
  };

  const handleStartChat = (patient) => {
    navigate('/doctor/chat', { state: { selectedPatient: patient } });
  };

  const handleVideoCall = (patient) => {
    // In a real app, this would initiate a video call
    alert(`Starting video call with ${patient.name}`);
  };

  const handleVoiceCall = (patient) => {
    // In a real app, this would initiate a voice call
    alert(`Starting voice call with ${patient.name}`);
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              My Patients
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage and communicate with your assigned patients
            </Typography>
          </Box>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            sx={{ borderRadius: 2 }}
          >
            Filter
          </Button>
        </Box>

        {/* Search */}
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            placeholder="Search patients by name, student ID, or department..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2
              }
            }}
          />
        </Box>

        {/* Patients Grid */}
        <Grid container spacing={3}>
          {filteredPatients.map((patient) => (
            <Grid item xs={12} md={6} lg={4} key={patient.id}>
              <Card 
                sx={{ 
                  borderRadius: 3, 
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  cursor: 'pointer',
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 30px rgba(0,0,0,0.12)'
                  }
                }}
                onClick={() => handlePatientClick(patient)}
              >
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar 
                      sx={{ 
                        width: 60, 
                        height: 60, 
                        mr: 2,
                        bgcolor: 'primary.light',
                        fontSize: '1.5rem'
                      }}
                    >
                      {patient.name.split(' ').map(n => n[0]).join('')}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" fontWeight="bold">
                        {patient.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {patient.studentId} • {patient.age} years old
                      </Typography>
                      <Chip 
                        label={patient.status} 
                        size="small" 
                        color="success" 
                        sx={{ mt: 0.5 }}
                      />
                    </Box>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Department:</strong> {patient.department}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Year:</strong> {patient.year}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Last Visit:</strong> {patient.lastVisit}
                    </Typography>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 1 }}>
                    <IconButton 
                      color="primary" 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleStartChat(patient);
                      }}
                      sx={{ 
                        bgcolor: 'primary.light',
                        '&:hover': { bgcolor: 'primary.main', color: 'white' }
                      }}
                    >
                      <ChatIcon />
                    </IconButton>
                    <IconButton 
                      color="success" 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleVideoCall(patient);
                      }}
                      sx={{ 
                        bgcolor: 'success.light',
                        '&:hover': { bgcolor: 'success.main', color: 'white' }
                      }}
                    >
                      <VideoCallIcon />
                    </IconButton>
                    <IconButton 
                      color="info" 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleVoiceCall(patient);
                      }}
                      sx={{ 
                        bgcolor: 'info.light',
                        '&:hover': { bgcolor: 'info.main', color: 'white' }
                      }}
                    >
                      <PhoneIcon />
                    </IconButton>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<EventIcon />}
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate('/doctor/appointments');
                      }}
                      sx={{ borderRadius: 2 }}
                    >
                      Schedule
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Patient Detail Dialog */}
        <Dialog 
          open={dialogOpen} 
          onClose={() => setDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          {selectedPatient && (
            <>
              <DialogTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ width: 50, height: 50, bgcolor: 'primary.light' }}>
                    {selectedPatient.name.split(' ').map(n => n[0]).join('')}
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="bold">
                      {selectedPatient.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {selectedPatient.studentId} • {selectedPatient.department}
                    </Typography>
                  </Box>
                </Box>
              </DialogTitle>
              <DialogContent>
                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                  <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                    <Tab label="Personal Info" />
                    <Tab label="Medical Info" />
                    <Tab label="Recent Activity" />
                  </Tabs>
                </Box>

                {tabValue === 0 && (
                  <Box>
                    <Typography variant="h6" gutterBottom>Personal Information</Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2"><strong>Age:</strong> {selectedPatient.age}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2"><strong>Gender:</strong> {selectedPatient.gender}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2"><strong>Year:</strong> {selectedPatient.year}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2"><strong>Status:</strong> {selectedPatient.status}</Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2"><strong>Email:</strong> {selectedPatient.contactInfo.email}</Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2"><strong>Phone:</strong> {selectedPatient.contactInfo.phone}</Typography>
                      </Grid>
                    </Grid>
                  </Box>
                )}

                {tabValue === 1 && (
                  <Box>
                    <Typography variant="h6" gutterBottom>Medical Information</Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Typography variant="body2"><strong>Blood Type:</strong> {selectedPatient.medicalInfo.bloodType}</Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2"><strong>Allergies:</strong> {selectedPatient.medicalInfo.allergies.join(', ')}</Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2"><strong>Conditions:</strong> {selectedPatient.medicalInfo.conditions.join(', ')}</Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2"><strong>Medications:</strong> {selectedPatient.medicalInfo.medications.join(', ')}</Typography>
                      </Grid>
                    </Grid>
                  </Box>
                )}

                {tabValue === 2 && (
                  <Box>
                    <Typography variant="h6" gutterBottom>Recent Activity</Typography>
                    <List>
                      {selectedPatient.recentActivity.map((activity, index) => (
                        <ListItem key={index} sx={{ px: 0 }}>
                          <ListItemText
                            primary={`${activity.type} - ${activity.date}`}
                            secondary={activity.notes}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setDialogOpen(false)}>Close</Button>
                <Button 
                  variant="contained" 
                  startIcon={<ChatIcon />}
                  onClick={() => {
                    setDialogOpen(false);
                    handleStartChat(selectedPatient);
                  }}
                >
                  Start Chat
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>
      </Container>
    </Layout>
  );
};

export default DoctorPatients;
