import React, { useState, useEffect } from 'react';
import { Container, Typography, Box, Paper, Tabs, Tab, Button, CircularProgress } from '@mui/material';
import { useFirebase } from '../../contexts/FirebaseContext';
import { getDoctorStudents } from '../../services/doctorStudentService';
import StudentManagementTable from './StudentManagementTable';
import StudentMetricsPanel from './StudentMetricsPanel';
import StudentMedicationsPanel from './StudentMedicationsPanel';
import StudentMedicalRecordsPanel from './StudentMedicalRecordsPanel';

const DoctorStudentManagementPage: React.FC = () => {
  const { currentUser } = useFirebase();
  const [students, setStudents] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [selectedStudent, setSelectedStudent] = useState<any | null>(null);

  useEffect(() => {
    if (!currentUser) return;
    setLoading(true);
    getDoctorStudents(currentUser.uid).then((data) => {
      setStudents(data);
      setLoading(false);
    });
  }, [currentUser]);

  const handleTabChange = (_: any, newValue: number) => setActiveTab(newValue);
  const handleSelectStudent = (student: any) => setSelectedStudent(student);

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" fontWeight="bold" gutterBottom>
        Student Management
      </Typography>
      <Paper sx={{ p: 2, borderRadius: 3, boxShadow: 2 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <StudentManagementTable students={students} onSelect={handleSelectStudent} />
            {selectedStudent && (
              <>
                <Tabs value={activeTab} onChange={handleTabChange} sx={{ mt: 2 }}>
                  <Tab label="Health Metrics" />
                  <Tab label="Medications" />
                  <Tab label="Medical Records" />
                </Tabs>
                <Box sx={{ mt: 2 }}>
                  {activeTab === 0 && <StudentMetricsPanel student={selectedStudent} />}
                  {activeTab === 1 && <StudentMedicationsPanel student={selectedStudent} />}
                  {activeTab === 2 && <StudentMedicalRecordsPanel student={selectedStudent} />}
                </Box>
              </>
            )}
          </>
        )}
      </Paper>
    </Container>
  );
};

export default DoctorStudentManagementPage;
