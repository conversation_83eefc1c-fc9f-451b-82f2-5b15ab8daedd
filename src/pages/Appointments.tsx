import React, { useState, useEffect, useCallback } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Grid,
  Button,
  Tabs,
  Tab,
  Card,
  CardContent,
  Chip,
  Divider
} from '@mui/material';
import Layout from '../components/layout/Layout';
import { useFirebase } from '../contexts/FirebaseContext';
import { getAppointmentsByStudent, getAppointmentsByDoctor, Appointment } from '../services/appointmentService';

const Appointments = () => {
  const [tabValue, setTabValue] = useState(0);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const { currentUser } = useFirebase();

  const fetchAppointments = useCallback(async () => {
    if (!currentUser) return;
    setLoading(true);
    let fetched: Appointment[] = [];
    try {
      if (currentUser.role === 'doctor') {
        fetched = await getAppointmentsByDoctor(currentUser.uid);
      } else if (currentUser.role === 'student') {
        fetched = await getAppointmentsByStudent(currentUser.uid);
      }
    } catch (e) {
      console.error('Error fetching appointments:', e);
    }
    setAppointments(fetched);
    setLoading(false);
  }, [currentUser]);

  useEffect(() => {
    fetchAppointments();
  }, [fetchAppointments]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Split appointments into upcoming and past
  const now = new Date();
  const upcomingAppointments = appointments.filter(a => {
    const aptDate = new Date(a.date + ' ' + a.time);
    return (a.status === 'scheduled' || a.status === 'confirmed' || a.status === 'pending') && aptDate >= now;
  });
  const pastAppointments = appointments.filter(a => {
    const aptDate = new Date(a.date + ' ' + a.time);
    return (a.status === 'completed' || a.status === 'cancelled' || a.status === 'no-show' || aptDate < now);
  });

  return (
    <Layout>
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            My Appointments
          </Typography>
          <Button variant="contained" color="primary">
            Book New Appointment
          </Button>
        </Box>
        
        <Paper elevation={0} sx={{ borderRadius: 2 }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange}
            sx={{ 
              borderBottom: 1, 
              borderColor: 'divider',
              '& .MuiTab-root': {
                py: 2
              }
            }}
          >
            <Tab label="Upcoming" />
            <Tab label="Past" />
          </Tabs>
          
          <Box sx={{ p: 3 }}>
            {tabValue === 0 && (
              <Grid container spacing={3}>
                {upcomingAppointments.length > 0 ? (
                  upcomingAppointments.map((appointment) => (
                    <Grid item xs={12} key={appointment.id}>
                      <Card variant="outlined">
                        <CardContent>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                            <Box>
                              <Typography variant="h6" fontWeight="bold">
                                {appointment.doctor}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                {appointment.specialty}
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                                <Box>
                                  <Typography variant="body2" color="text.secondary">
                                    Date
                                  </Typography>
                                  <Typography variant="body1" fontWeight="medium">
                                    {appointment.date}
                                  </Typography>
                                </Box>
                                <Box>
                                  <Typography variant="body2" color="text.secondary">
                                    Time
                                  </Typography>
                                  <Typography variant="body1" fontWeight="medium">
                                    {appointment.time}
                                  </Typography>
                                </Box>
                              </Box>
                            </Box>
                            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                              <Chip 
                                label={appointment.status} 
                                color={appointment.status === 'Confirmed' ? 'success' : 'warning'}
                                size="small"
                              />
                              <Box sx={{ mt: 2 }}>
                                <Button variant="outlined" size="small" sx={{ mr: 1 }}>
                                  Reschedule
                                </Button>
                                <Button variant="outlined" color="error" size="small">
                                  Cancel
                                </Button>
                              </Box>
                            </Box>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))
                ) : (
                  <Typography variant="body1" sx={{ py: 4, textAlign: 'center' }}>
                    No upcoming appointments.
                  </Typography>
                )}
              </Grid>
            )}
            
            {tabValue === 1 && (
              <Grid container spacing={3}>
                {pastAppointments.length > 0 ? (
                  pastAppointments.map((appointment) => (
                    <Grid item xs={12} key={appointment.id}>
                      <Card variant="outlined">
                        <CardContent>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                            <Box>
                              <Typography variant="h6" fontWeight="bold">
                                {appointment.doctor}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                {appointment.specialty}
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                                <Box>
                                  <Typography variant="body2" color="text.secondary">
                                    Date
                                  </Typography>
                                  <Typography variant="body1" fontWeight="medium">
                                    {appointment.date}
                                  </Typography>
                                </Box>
                                <Box>
                                  <Typography variant="body2" color="text.secondary">
                                    Time
                                  </Typography>
                                  <Typography variant="body1" fontWeight="medium">
                                    {appointment.time}
                                  </Typography>
                                </Box>
                              </Box>
                            </Box>
                            <Box>
                              <Chip 
                                label={appointment.status} 
                                color="default"
                                size="small"
                              />
                              <Button 
                                variant="text" 
                                color="primary" 
                                size="small"
                                sx={{ display: 'block', mt: 2 }}
                              >
                                View Details
                              </Button>
                            </Box>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))
                ) : (
                  <Typography variant="body1" sx={{ py: 4, textAlign: 'center' }}>
                    No past appointments.
                  </Typography>
                )}
              </Grid>
            )}
          </Box>
        </Paper>
      </Container>
    </Layout>
  );
};

export default Appointments;