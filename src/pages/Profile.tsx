import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Grid,
  Divider,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tab,
  Tabs,
  Card,
  CardContent,
  LinearProgress,
  Alert,
  Snackbar,
  CircularProgress
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Layout from '../components/layout/Layout';
import EditIcon from '@mui/icons-material/Edit';
import RefreshIcon from '@mui/icons-material/Refresh';
import EmailIcon from '@mui/icons-material/Email';
import SchoolIcon from '@mui/icons-material/School';
import EventIcon from '@mui/icons-material/Event';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import BloodtypeIcon from '@mui/icons-material/Bloodtype';
import ContactPhoneIcon from '@mui/icons-material/ContactPhone';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import PersonIcon from '@mui/icons-material/Person';
import { checkProfileCompletion, getCompletionSummary } from '../services/profileCompletionService';
import StudentProfileForm from '../components/StudentProfileForm';
import type { UserProfile, ProfileCompletionResult } from '../types/firebase';

const Profile: React.FC = () => {
  const navigate = useNavigate();
  const { userProfile, refreshUserProfile, currentUser } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [completionStatus, setCompletionStatus] = useState<ProfileCompletionResult | null>(null);

  // Check profile completion status
  useEffect(() => {
    if (userProfile) {
      const status = checkProfileCompletion(userProfile);
      setCompletionStatus(status);
    }
  }, [userProfile]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleEditProfile = () => {
    setShowEditForm(true);
  };

  const handleCloseEditForm = () => {
    setShowEditForm(false);
    // Refresh profile data after editing
    refreshUserProfile();
  };

  const handleCloseSnackbar = () => {
    setSaveSuccess(false);
  };

  const handleRefreshProfile = async () => {
    setRefreshing(true);
    try {
      await refreshUserProfile();
      setSaveSuccess(true);
    } catch (error) {
      console.error('Error refreshing profile:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Generate avatar text from name
  const generateAvatar = (name: string) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return name.substring(0, 2).toUpperCase();
  };

  // Get display name from user profile
  const getDisplayName = () => {
    if (userProfile?.firstName && userProfile?.lastName) {
      return `${userProfile.firstName} ${userProfile.lastName}`;
    }
    return userProfile?.displayName || currentUser?.displayName || 'Student';
  };

  // Format date for display
  const formatDate = (date: Date | string) => {
    if (!date) return 'Not provided';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString();
  };

  if (!userProfile) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold">
              My Profile
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Current Role: <strong>{userProfile.role}</strong>
              {userProfile.studentId && (
                <> • Student ID: <strong>{userProfile.studentId}</strong></>
              )}
            </Typography>
          </Box>
          <Box>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefreshProfile}
              disabled={refreshing}
              sx={{ borderRadius: 2, mr: 2 }}
            >
              {refreshing ? 'Refreshing...' : 'Refresh Profile'}
            </Button>
            <Button
              variant="contained"
              startIcon={<EditIcon />}
              onClick={handleEditProfile}
              sx={{ borderRadius: 2 }}
            >
              Edit Profile
            </Button>
          </Box>
        </Box>

        {/* Profile Completion Status */}
        {completionStatus && !completionStatus.isComplete && (
          <Alert severity="warning" sx={{ mb: 4 }}>
            <Typography variant="body2">
              <strong>Profile Incomplete:</strong> {getCompletionSummary(completionStatus)}
            </Typography>
            <LinearProgress
              variant="determinate"
              value={completionStatus.completionPercentage}
              sx={{ mt: 1 }}
            />
          </Alert>
        )}

        {/* Student Profile */}
        <Paper
          sx={{
            p: 4,
            mb: 4,
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={4}>
            {/* Avatar and Basic Info */}
            <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Avatar
                sx={{
                  width: 150,
                  height: 150,
                  fontSize: '3rem',
                  mb: 2,
                  bgcolor: 'secondary.main'
                }}
              >
                {generateAvatar(getDisplayName())}
              </Avatar>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {getDisplayName()}
              </Typography>
              <Chip
                label={userProfile.status === 'active' ? 'Active Student' : 'Inactive'}
                color={userProfile.status === 'active' ? 'success' : 'default'}
                variant="outlined"
                sx={{ mb: 1 }}
              />
              {userProfile.department && userProfile.year && (
                <Chip
                  label={`${userProfile.department} - ${userProfile.year}`}
                  color="secondary"
                  sx={{ mb: 1 }}
                />
              )}
              {userProfile.studentId && (
                <Chip
                  label={`ID: ${userProfile.studentId}`}
                  variant="outlined"
                  sx={{ mb: 3 }}
                />
              )}
              {completionStatus && (
                <Chip
                  label={`Profile ${completionStatus.completionPercentage}% Complete`}
                  color={completionStatus.isComplete ? 'success' : 'warning'}
                  variant="filled"
                />
              )}
            </Grid>

            {/* Student Information */}
            <Grid item xs={12} md={8}>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Student Information
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <EmailIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Email
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.email || 'Not provided'}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <SchoolIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Department
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.department || 'Not provided'}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <EventIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Enrollment Date
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {formatDate(userProfile.createdAt)}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <ContactPhoneIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Phone
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.phoneNumber || 'Not provided'}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Paper>

        {/* Tabs for additional information */}
        <Paper
          sx={{
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{ borderBottom: 1, borderColor: 'divider', px: 3 }}
          >
            <Tab label="Personal Details" />
            <Tab label="Medical Information" />
            <Tab label="Emergency Contact" />
          </Tabs>

          <Box sx={{ p: 4 }}>
            {tabValue === 0 && (
              <Box>
                <Typography variant="h6" fontWeight="medium" gutterBottom>
                  Personal Details
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        First Name
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.firstName || 'Not provided'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Last Name
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.lastName || 'Not provided'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Date of Birth
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.dateOfBirth ? formatDate(userProfile.dateOfBirth) : 'Not provided'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Gender
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.gender || 'Not provided'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Academic Year
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.year || 'Not provided'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Major
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.major || 'Not provided'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Address
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.address ?
                          `${userProfile.address}${userProfile.city ? `, ${userProfile.city}` : ''}${userProfile.state ? `, ${userProfile.state}` : ''}${userProfile.zipCode ? ` ${userProfile.zipCode}` : ''}`
                          : 'Not provided'
                        }
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            )}

            {tabValue === 1 && (
              <Box>
                <Typography variant="h6" fontWeight="medium" gutterBottom>
                  Medical Information
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <BloodtypeIcon sx={{ mr: 1, color: 'error.main' }} />
                        <Typography variant="subtitle1" fontWeight="medium">
                          Blood Type
                        </Typography>
                      </Box>
                      <Typography variant="h6" color="error.main">
                        {userProfile.medicalInfo?.bloodType || 'Not provided'}
                      </Typography>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <HealthAndSafetyIcon sx={{ mr: 1, color: 'warning.main' }} />
                        <Typography variant="subtitle1" fontWeight="medium">
                          Allergies
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {userProfile.medicalInfo?.allergies && userProfile.medicalInfo.allergies.length > 0 ? (
                          userProfile.medicalInfo.allergies.map((allergy, index) => (
                            <Chip
                              key={index}
                              label={allergy}
                              color="warning"
                              variant="outlined"
                              size="small"
                            />
                          ))
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            No allergies recorded
                          </Typography>
                        )}
                      </Box>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <MedicalServicesIcon sx={{ mr: 1, color: 'primary.main' }} />
                        <Typography variant="subtitle1" fontWeight="medium">
                          Current Medications
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {userProfile.medicalInfo?.medications && userProfile.medicalInfo.medications.length > 0 ? (
                          userProfile.medicalInfo.medications.map((medication, index) => (
                            <Chip
                              key={index}
                              label={medication}
                              color="primary"
                              variant="outlined"
                              size="small"
                            />
                          ))
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            No medications recorded
                          </Typography>
                        )}
                      </Box>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <LocalHospitalIcon sx={{ mr: 1, color: 'info.main' }} />
                        <Typography variant="subtitle1" fontWeight="medium">
                          Medical Conditions
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {userProfile.medicalInfo?.conditions && userProfile.medicalInfo.conditions.length > 0 ? (
                          userProfile.medicalInfo.conditions.map((condition, index) => (
                            <Chip
                              key={index}
                              label={condition}
                              color="info"
                              variant="outlined"
                              size="small"
                            />
                          ))
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            No medical conditions recorded
                          </Typography>
                        )}
                      </Box>
                    </Card>
                  </Grid>

                  {userProfile.medicalInfo?.insuranceProvider && (
                    <Grid item xs={12} md={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Insurance Provider
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {userProfile.medicalInfo.insuranceProvider}
                        </Typography>
                      </Box>
                    </Grid>
                  )}

                  {userProfile.medicalInfo?.primaryPhysician && (
                    <Grid item xs={12} md={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Primary Physician
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {userProfile.medicalInfo.primaryPhysician}
                          {userProfile.medicalInfo.primaryPhysicianPhone &&
                            ` - ${userProfile.medicalInfo.primaryPhysicianPhone}`
                          }
                        </Typography>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </Box>
            )}

            {tabValue === 2 && (
              <Box>
                <Typography variant="h6" fontWeight="medium" gutterBottom>
                  Emergency Contact
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Contact Name
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.emergencyContact?.name || 'Not provided'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Relationship
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.emergencyContact?.relationship || 'Not provided'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Phone Number
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.emergencyContact?.phone || 'Not provided'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Email Address
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {userProfile.emergencyContact?.email || 'Not provided'}
                      </Typography>
                    </Box>
                  </Grid>
                  {userProfile.emergencyContact?.address && (
                    <Grid item xs={12}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Address
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {userProfile.emergencyContact.address}
                        </Typography>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </Box>
            )}
          </Box>
        </Paper>
      </Container>

      {/* Success Snackbar */}
      <Snackbar
        open={saveSuccess}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          {refreshing ? 'Profile refreshed successfully!' : 'Profile updated successfully!'}
        </Alert>
      </Snackbar>

      {/* Edit Profile Form */}
      <StudentProfileForm
        open={showEditForm}
        onClose={handleCloseEditForm}
        isOverlay={false}
      />
    </Layout>
  );
};

export default Profile;