import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Grid,
  Divider,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Rating,
  CircularProgress,
  Alert
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import EmailIcon from '@mui/icons-material/Email';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import EventIcon from '@mui/icons-material/Event';
import PhoneIcon from '@mui/icons-material/Phone';
import StarIcon from '@mui/icons-material/Star';
import ScheduleIcon from '@mui/icons-material/Schedule';
import SchoolIcon from '@mui/icons-material/School';
import WorkIcon from '@mui/icons-material/Work';
import LanguageIcon from '@mui/icons-material/Language';
import { getUserById } from '../../services/userManagementService';
import { getAppointmentsByDoctor } from '../../services/appointmentService';
import type { UserProfile } from '../../types/firebase';

const AdminDoctorDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(true);
  const [doctor, setDoctor] = useState<UserProfile | null>(null);
  const [appointments, setAppointments] = useState<any[]>([]);
  const [tabValue, setTabValue] = useState(0);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDoctorData = async () => {
      if (!id) return;

      setLoading(true);
      try {
        // Fetch doctor profile
        const doctorData = await getUserById(id);
        if (!doctorData || doctorData.role !== 'doctor') {
          setError('Doctor not found');
          return;
        }

        setDoctor(doctorData);

        // Fetch doctor's appointments
        try {
          const appointmentsData = await getAppointmentsByDoctor(id);
          setAppointments(appointmentsData || []);
        } catch (appointmentError) {
          console.error('Error fetching appointments:', appointmentError);
          // Don't set error for appointments, just use empty array
          setAppointments([]);
        }

      } catch (err) {
        console.error('Error fetching doctor:', err);
        setError('Failed to load doctor data');
      } finally {
        setLoading(false);
      }
    };

    fetchDoctorData();
  }, [id]);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Generate avatar text from name
  const generateAvatar = (name: string) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return name.substring(0, 2).toUpperCase();
  };

  // Format date helper
  const formatDate = (date: any): string => {
    if (!date) return 'N/A';

    try {
      if (date.toDate && typeof date.toDate === 'function') {
        return date.toDate().toLocaleDateString();
      }
      if (date instanceof Date) {
        return date.toLocaleDateString();
      }
      return new Date(date).toLocaleDateString();
    } catch {
      return 'Invalid Date';
    }
  };

  // Calculate average rating from appointments (mock calculation)
  const calculateRating = (): number => {
    // In a real app, this would come from actual reviews
    return 4.7;
  };

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Container>
      </Layout>
    );
  }

  if (error || !doctor) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Alert severity="error" sx={{ mb: 2 }}>
            {error || 'Doctor not found'}
          </Alert>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/admin/doctors')}
          >
            Back to Doctors
          </Button>
        </Container>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Doctor Details
          </Typography>
          <Box>
            <Button 
              variant="outlined" 
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/admin/doctors')}
              sx={{ borderRadius: 2, mr: 2 }}
            >
              Back to Doctors
            </Button>
            <Button 
              variant="contained" 
              startIcon={<EditIcon />}
              onClick={() => navigate(`/admin/doctors/${id}/edit`)}
              sx={{ borderRadius: 2 }}
            >
              Edit Doctor
            </Button>
          </Box>
        </Box>
        
        {/* Doctor Profile */}
        <Paper 
          sx={{ 
            p: 4, 
            mb: 4,
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={4}>
            {/* Avatar and Basic Info */}
            <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Avatar
                sx={{
                  width: 150,
                  height: 150,
                  fontSize: '3rem',
                  mb: 2,
                  bgcolor: 'primary.main'
                }}
              >
                {generateAvatar(doctor.displayName || '')}
              </Avatar>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {doctor.displayName}
              </Typography>
              <Chip
                label={(doctor.status || 'active') === 'active' ? 'Active' : 'Inactive'}
                color={(doctor.status || 'active') === 'active' ? 'success' : 'default'}
                variant="outlined"
                sx={{ mb: 1 }}
              />
              <Chip
                label={doctor.specialty || 'General Practice'}
                color="primary"
                sx={{ mb: 2 }}
              />

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Rating
                  value={calculateRating()}
                  precision={0.1}
                  readOnly
                  size="small"
                  emptyIcon={<StarIcon style={{ opacity: 0.55 }} fontSize="inherit" />}
                />
                <Typography variant="body2" sx={{ ml: 1 }}>
                  {calculateRating()} / 5.0
                </Typography>
              </Box>

              <Box sx={{ textAlign: 'center', mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Total Appointments
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {appointments.length}
                </Typography>
              </Box>
            </Grid>
            
            {/* Contact and Details */}
            <Grid item xs={12} md={8}>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Contact Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <List disablePadding>
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <EmailIcon color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Email Address"
                    secondary={doctor.email}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>

                {doctor.phone && (
                  <ListItem disableGutters>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <PhoneIcon color="action" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Phone Number"
                      secondary={doctor.phone}
                      primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                      secondaryTypographyProps={{ variant: 'body1' }}
                    />
                  </ListItem>
                )}

                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <EventIcon color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Join Date"
                    secondary={formatDate(doctor.createdAt)}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
              </List>
              
              <Typography variant="h6" fontWeight="medium" gutterBottom sx={{ mt: 3 }}>
                Professional Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <List disablePadding>
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <LocalHospitalIcon color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Specialty"
                    secondary={doctor.specialty || 'General Practice'}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>

                {doctor.department && (
                  <ListItem disableGutters>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <WorkIcon color="action" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Department"
                      secondary={doctor.department}
                      primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                      secondaryTypographyProps={{ variant: 'body1' }}
                    />
                  </ListItem>
                )}

                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <ScheduleIcon color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Status"
                    secondary={(doctor.status || 'active') === 'active' ? 'Available' : 'Unavailable'}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>

                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <EventIcon color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Last Updated"
                    secondary={formatDate(doctor.updatedAt)}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
              </List>
            </Grid>
          </Grid>
        </Paper>
        
        {/* Tabs for Appointments and Reviews */}
        <Paper 
          sx={{ 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden'
          }}
        >
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="doctor details tabs"
              sx={{ px: 2 }}
            >
              <Tab label="Appointments" />
              <Tab label="Details" />
            </Tabs>
          </Box>
          
          {/* Appointments Tab */}
          <Box role="tabpanel" hidden={tabValue !== 0} sx={{ p: 3 }}>
            {tabValue === 0 && (
              <>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="h6" fontWeight="medium">
                    Appointment Schedule
                  </Typography>
                  <Button 
                    variant="contained" 
                    color="primary"
                    size="small"
                    onClick={() => navigate('/admin/appointments/new')}
                    sx={{ borderRadius: 2 }}
                  >
                    Schedule New Appointment
                  </Button>
                </Box>
                
                <TableContainer>
                  <Table sx={{ minWidth: 650 }}>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: 'rgba(0,0,0,0.03)' }}>
                        <TableCell>Date</TableCell>
                        <TableCell>Time</TableCell>
                        <TableCell>Student</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {appointments.map((appointment) => (
                        <TableRow key={appointment.id}>
                          <TableCell>{formatDate(appointment.date)}</TableCell>
                          <TableCell>{appointment.time || 'N/A'}</TableCell>
                          <TableCell>{appointment.studentName || appointment.patientName || 'N/A'}</TableCell>
                          <TableCell>{appointment.type || appointment.reason || 'General Consultation'}</TableCell>
                          <TableCell>
                            <Chip
                              label={appointment.status ? appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1) : 'Scheduled'}
                              color={
                                appointment.status === 'completed' ? 'success' :
                                appointment.status === 'scheduled' ? 'primary' :
                                appointment.status === 'cancelled' ? 'error' :
                                'default'
                              }
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Button
                              size="small"
                              onClick={() => navigate(`/admin/appointments/${appointment.id}`)}
                            >
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      {appointments.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                            <Typography variant="body1" color="text.secondary">
                              No appointments found for this doctor
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}
          </Box>
          
          {/* Reviews Tab */}
          <Box role="tabpanel" hidden={tabValue !== 1} sx={{ p: 3 }}>
            {tabValue === 1 && (
              <>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h6" fontWeight="medium">
                    Doctor Information
                  </Typography>
                </Box>

                <List>
                  <ListItem>
                    <ListItemIcon>
                      <EmailIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email Address"
                      secondary={doctor.email}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <LocalHospitalIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Specialty"
                      secondary={doctor.specialty || 'General Practice'}
                    />
                  </ListItem>

                  {doctor.department && (
                    <ListItem>
                      <ListItemIcon>
                        <WorkIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary="Department"
                        secondary={doctor.department}
                      />
                    </ListItem>
                  )}

                  <ListItem>
                    <ListItemIcon>
                      <EventIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Account Created"
                      secondary={formatDate(doctor.createdAt)}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <ScheduleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Total Appointments"
                      secondary={`${appointments.length} appointments`}
                    />
                  </ListItem>
                </List>
              </>
            )}
          </Box>
        </Paper>
      </Container>
    </Layout>
  );
};

export default AdminDoctorDetail;