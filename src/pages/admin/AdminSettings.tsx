import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Grid,
  TextField,
  Divider,
  Switch,
  FormControlLabel,
  FormGroup,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Snackbar,
  Alert,
  CircularProgress
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import { getSystemSettings, updateSystemSettings, resetSystemSettings, type SystemSettings } from '../../services/systemSettingsService';
import { useAuth } from '../../contexts/AuthContext';

// Icons
import SettingsIcon from '@mui/icons-material/Settings';
import SecurityIcon from '@mui/icons-material/Security';
import NotificationsIcon from '@mui/icons-material/Notifications';
import StorageIcon from '@mui/icons-material/Storage';
import LanguageIcon from '@mui/icons-material/Language';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import SaveIcon from '@mui/icons-material/Save';
import RestoreIcon from '@mui/icons-material/Restore';

const AdminSettings = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [activeSection, setActiveSection] = useState('general');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // System settings state
  const [settings, setSettings] = useState<SystemSettings | null>(null);

  // Load settings on component mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setLoading(true);
        const systemSettings = await getSystemSettings();
        setSettings(systemSettings);
      } catch (error) {
        console.error('Error loading settings:', error);
        setSnackbarMessage('Failed to load settings');
        setSnackbarSeverity('error');
        setSnackbarOpen(true);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, []);
  
  const handleSaveSettings = async () => {
    if (!settings || !currentUser) return;

    try {
      setSaving(true);
      await updateSystemSettings(settings, currentUser.uid);
      setSnackbarMessage('Settings saved successfully!');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Error saving settings:', error);
      setSnackbarMessage('Failed to save settings. Please try again.');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setSaving(false);
    }
  };

  const handleResetSettings = async () => {
    if (!currentUser) return;

    try {
      setSaving(true);
      await resetSystemSettings(currentUser.uid);
      const resetSettings = await getSystemSettings();
      setSettings(resetSettings);
      setSnackbarMessage('Settings reset to default successfully!');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Error resetting settings:', error);
      setSnackbarMessage('Failed to reset settings. Please try again.');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setSaving(false);
    }
  };

  const handleCloseSnackbar = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  };

  const updateSetting = (key: keyof SystemSettings, value: any) => {
    if (settings) {
      setSettings({
        ...settings,
        [key]: value
      });
    }
  };

  const renderSettingsContent = () => {
    switch (activeSection) {
      case 'general':
        return (
          <>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              General Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  label="Site Name"
                  value={settings?.siteName || ''}
                  onChange={(e) => updateSetting('siteName', e.target.value)}
                  fullWidth
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Site Description"
                  value={settings?.siteDescription || ''}
                  onChange={(e) => updateSetting('siteDescription', e.target.value)}
                  fullWidth
                  variant="outlined"
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Contact Email"
                  value={settings?.contactEmail || ''}
                  onChange={(e) => updateSetting('contactEmail', e.target.value)}
                  fullWidth
                  variant="outlined"
                  type="email"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Support Phone"
                  value={settings?.supportPhone || ''}
                  onChange={(e) => updateSetting('supportPhone', e.target.value)}
                  fullWidth
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings?.maintenanceMode || false}
                        onChange={(e) => updateSetting('maintenanceMode', e.target.checked)}
                      />
                    }
                    label="Maintenance Mode"
                  />
                  <Typography variant="caption" color="text.secondary">
                    When enabled, the site will display a maintenance message to all non-admin users.
                  </Typography>
                </FormGroup>
              </Grid>
              {settings?.maintenanceMode && (
                <Grid item xs={12}>
                  <TextField
                    label="Maintenance Message"
                    value={settings?.maintenanceMessage || ''}
                    onChange={(e) => updateSetting('maintenanceMessage', e.target.value)}
                    fullWidth
                    variant="outlined"
                    multiline
                    rows={2}
                  />
                </Grid>
              )}
            </Grid>
          </>
        );
      case 'security':
        return (
          <>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Security Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Password Minimum Length"
                  type="number"
                  value={settings?.passwordMinLength || 8}
                  onChange={(e) => updateSetting('passwordMinLength', parseInt(e.target.value))}
                  fullWidth
                  variant="outlined"
                  InputProps={{ inputProps: { min: 6, max: 20 } }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Session Timeout (minutes)"
                  type="number"
                  value={settings?.sessionTimeoutMinutes || 60}
                  onChange={(e) => updateSetting('sessionTimeoutMinutes', parseInt(e.target.value))}
                  fullWidth
                  variant="outlined"
                  InputProps={{ inputProps: { min: 5, max: 120 } }}
                />
              </Grid>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings?.passwordRequireSpecialChars || false}
                        onChange={(e) => updateSetting('passwordRequireSpecialChars', e.target.checked)}
                      />
                    }
                    label="Require Special Characters in Password"
                  />
                  <Typography variant="caption" color="text.secondary">
                    Require passwords to contain special characters.
                  </Typography>
                </FormGroup>
              </Grid>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings?.requireTwoFactor || false}
                        onChange={(e) => updateSetting('requireTwoFactor', e.target.checked)}
                      />
                    }
                    label="Two-Factor Authentication"
                  />
                  <Typography variant="caption" color="text.secondary">
                    Require two-factor authentication for all admin users.
                  </Typography>
                </FormGroup>
              </Grid>
            </Grid>
          </>
        );
      case 'notifications':
        return (
          <>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Notification Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings?.emailNotifications || false}
                        onChange={(e) => updateSetting('emailNotifications', e.target.checked)}
                      />
                    }
                    label="Email Notifications"
                  />
                </FormGroup>
              </Grid>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings?.smsNotifications || false}
                        onChange={(e) => updateSetting('smsNotifications', e.target.checked)}
                      />
                    }
                    label="SMS Notifications"
                  />
                </FormGroup>
              </Grid>
              <Grid item xs={12}>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings?.appointmentReminders || false}
                        onChange={(e) => updateSetting('appointmentReminders', e.target.checked)}
                      />
                    }
                    label="Appointment Reminders"
                  />
                </FormGroup>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Reminder Time (hours before appointment)"
                  type="number"
                  value={settings?.reminderHoursBefore || 24}
                  onChange={(e) => updateSetting('reminderHoursBefore', parseInt(e.target.value))}
                  fullWidth
                  variant="outlined"
                  disabled={!settings?.appointmentReminders}
                  InputProps={{ inputProps: { min: 1, max: 72 } }}
                />
              </Grid>
            </Grid>
          </>
        );
      default:
        return null;
    }
  };

  // Show loading state
  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '50vh'
          }}>
            <CircularProgress size={60} />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            System Settings
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Configure and manage system-wide settings
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {/* Settings Navigation */}
          <Grid item xs={12} md={3}>
            <Paper 
              sx={{ 
                p: 0, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                overflow: 'hidden'
              }}
            >
              <List component="nav" sx={{ p: 0 }}>
                <ListItem 
                  button 
                  selected={activeSection === 'general'}
                  onClick={() => setActiveSection('general')}
                  sx={{ 
                    py: 2,
                    borderLeft: activeSection === 'general' ? 4 : 0,
                    borderColor: 'primary.main',
                    '&.Mui-selected': {
                      bgcolor: 'rgba(0,114,255,0.08)'
                    }
                  }}
                >
                  <ListItemIcon>
                    <SettingsIcon color={activeSection === 'general' ? 'primary' : 'inherit'} />
                  </ListItemIcon>
                  <ListItemText primary="General Settings" />
                </ListItem>
                <ListItem 
                  button 
                  selected={activeSection === 'security'}
                  onClick={() => setActiveSection('security')}
                  sx={{ 
                    py: 2,
                    borderLeft: activeSection === 'security' ? 4 : 0,
                    borderColor: 'primary.main',
                    '&.Mui-selected': {
                      bgcolor: 'rgba(0,114,255,0.08)'
                    }
                  }}
                >
                  <ListItemIcon>
                    <SecurityIcon color={activeSection === 'security' ? 'primary' : 'inherit'} />
                  </ListItemIcon>
                  <ListItemText primary="Security Settings" />
                </ListItem>
                <ListItem 
                  button 
                  selected={activeSection === 'notifications'}
                  onClick={() => setActiveSection('notifications')}
                  sx={{ 
                    py: 2,
                    borderLeft: activeSection === 'notifications' ? 4 : 0,
                    borderColor: 'primary.main',
                    '&.Mui-selected': {
                      bgcolor: 'rgba(0,114,255,0.08)'
                    }
                  }}
                >
                  <ListItemIcon>
                    <NotificationsIcon color={activeSection === 'notifications' ? 'primary' : 'inherit'} />
                  </ListItemIcon>
                  <ListItemText primary="Notification Settings" />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          {/* Settings Content */}
          <Grid item xs={12} md={9}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              {renderSettingsContent()}
              
              <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<RestoreIcon />}
                  onClick={handleResetSettings}
                  disabled={saving}
                  sx={{ borderRadius: 2 }}
                >
                  Reset to Default
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                  onClick={handleSaveSettings}
                  disabled={saving}
                  sx={{ borderRadius: 2 }}
                >
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Container>

      <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleCloseSnackbar}>
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default AdminSettings;