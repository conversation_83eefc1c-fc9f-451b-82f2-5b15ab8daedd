import React, { useState, useEffect } from 'react';
import {
  Container, Typography, Box, Grid, Paper, Card, CardContent,
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow,
  Chip, Button, Dialog, DialogTitle, DialogContent, DialogActions,
  List, ListItem, ListItemText, Avatar, Divider, Alert, LinearProgress
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import { getAssignmentStats, getAllDoctors, type Doctor } from '../../services/doctorAssignmentService';
import PeopleIcon from '@mui/icons-material/People';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import AssignmentIcon from '@mui/icons-material/Assignment';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';

const AdminDoctorAssignments = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<any>(null);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load assignment statistics and doctor data
    const loadData = async () => {
      try {
        const assignmentStats = getAssignmentStats();
        const allDoctors = getAllDoctors();
        
        setStats(assignmentStats);
        setDoctors(allDoctors);
        setLoading(false);
      } catch (error) {
        console.error('Error loading assignment data:', error);
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const handleViewDoctorDetails = (doctor: Doctor) => {
    setSelectedDoctor(doctor);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedDoctor(null);
  };

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <LinearProgress sx={{ width: '50%' }} />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Box sx={{ bgcolor: '#f5f7fa', minHeight: '100vh', py: 4 }}>
        <Container maxWidth="lg">
          {/* Header */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Doctor Assignment Management
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Monitor and manage healthcare provider assignments to students
            </Typography>
          </Box>

          {/* Statistics Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <LocalHospitalIcon sx={{ color: 'primary.main', mr: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      Total Doctors
                    </Typography>
                  </Box>
                  <Typography variant="h3" fontWeight="bold" color="primary.main">
                    {stats?.totalDoctors || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Available healthcare providers
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <PeopleIcon sx={{ color: 'success.main', mr: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      Total Students
                    </Typography>
                  </Box>
                  <Typography variant="h3" fontWeight="bold" color="success.main">
                    {stats?.totalStudents || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Registered students
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <AssignmentIcon sx={{ color: 'warning.main', mr: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      Total Assignments
                    </Typography>
                  </Box>
                  <Typography variant="h3" fontWeight="bold" color="warning.main">
                    {stats?.totalAssignments || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active doctor-student pairs
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <TrendingUpIcon sx={{ color: 'info.main', mr: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      Avg per Student
                    </Typography>
                  </Box>
                  <Typography variant="h3" fontWeight="bold" color="info.main">
                    {stats?.avgAssignmentsPerStudent || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Doctors assigned per student
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Assignment Strategy Info */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  🎯 Smart Assignment Strategy
                </Typography>
                <Alert severity="info" sx={{ mb: 2 }}>
                  Every student automatically receives essential healthcare coverage
                </Alert>
                <List dense>
                  <ListItem>
                    <ListItemText 
                      primary="✅ Core Specialists (100% Coverage)"
                      secondary="General Practice, Mental Health, Emergency Medicine"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="🎲 Additional Specialists (2 Random)"
                      secondary="Balanced distribution across all specialties"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="📋 Request System"
                      secondary="Students can request additional specialists as needed"
                    />
                  </ListItem>
                </List>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  📊 Specialty Demand
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Most Requested: {stats?.mostRequestedSpecialty}
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={85} 
                    sx={{ height: 8, borderRadius: 4, bgcolor: 'success.light' }}
                  />
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Core Specialties Coverage: {stats?.coreSpecialtiesCoverage}
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={100} 
                    color="success"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Least Requested: {stats?.leastRequestedSpecialty}
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={25} 
                    color="warning"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </Paper>
            </Grid>
          </Grid>

          {/* Doctors Table */}
          <Paper sx={{ borderRadius: 3, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
            <Box sx={{ p: 3, borderBottom: '1px solid', borderColor: 'divider' }}>
              <Typography variant="h6" fontWeight="bold">
                Healthcare Providers Directory
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Manage and monitor all available healthcare providers
              </Typography>
            </Box>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Doctor</TableCell>
                    <TableCell>Specialty</TableCell>
                    <TableCell>Department</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Assignment Type</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {doctors.map((doctor) => (
                    <TableRow key={doctor.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                            {doctor.name.charAt(0)}
                          </Avatar>
                          <Typography variant="body2" fontWeight="medium">
                            {doctor.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{doctor.specialty}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {doctor.department}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={doctor.online ? 'Online' : 'Offline'}
                          color={doctor.online ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={['General Practice', 'Mental Health', 'Emergency Medicine'].includes(doctor.specialty) ? 'Core' : 'Specialist'}
                          color={['General Practice', 'Mental Health', 'Emergency Medicine'].includes(doctor.specialty) ? 'primary' : 'secondary'}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Button 
                          size="small" 
                          variant="outlined"
                          onClick={() => handleViewDoctorDetails(doctor)}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>

          {/* Doctor Details Dialog */}
          <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                  {selectedDoctor?.name.charAt(0)}
                </Avatar>
                <Box>
                  <Typography variant="h6">{selectedDoctor?.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedDoctor?.specialty} • {selectedDoctor?.department}
                  </Typography>
                </Box>
              </Box>
            </DialogTitle>
            <DialogContent>
              <Box sx={{ py: 2 }}>
                <Typography variant="body1" gutterBottom>
                  <strong>Assignment Type:</strong> {' '}
                  {['General Practice', 'Mental Health', 'Emergency Medicine'].includes(selectedDoctor?.specialty || '') 
                    ? 'Core Specialist (Auto-assigned to all students)' 
                    : 'Additional Specialist (Assigned randomly or by request)'}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  <strong>Status:</strong> {selectedDoctor?.online ? '🟢 Online' : '🔴 Offline'}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  <strong>Estimated Assignments:</strong> ~{Math.floor(Math.random() * 50) + 20} students
                </Typography>
                <Typography variant="body1">
                  <strong>Availability:</strong> Monday - Friday, 9:00 AM - 5:00 PM
                </Typography>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog}>Close</Button>
            </DialogActions>
          </Dialog>
        </Container>
      </Box>
    </Layout>
  );
};

export default AdminDoctorAssignments;
