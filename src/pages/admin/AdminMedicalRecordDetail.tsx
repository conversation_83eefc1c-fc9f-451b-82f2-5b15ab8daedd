import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  Grid,
  Divider,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Tab,
  Tabs
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import MedicalRecordImages from '../../components/medical/MedicalRecordImages';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import EventIcon from '@mui/icons-material/Event';
import PersonIcon from '@mui/icons-material/Person';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';
import HealingIcon from '@mui/icons-material/Healing';
import NoteIcon from '@mui/icons-material/Note';
import ImageIcon from '@mui/icons-material/Image';

const AdminMedicalRecordDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(true);
  const [medicalRecord, setMedicalRecord] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [medicalImages, setMedicalImages] = useState([]);
  
  useEffect(() => {
    // In a real app, you would fetch the medical record data from an API
    setLoading(true);
    setTimeout(() => {
      // Mock medical record data
      const mockMedicalRecord = {
        id: parseInt(id),
        student: {
          id: 1,
          name: 'John Smith',
          email: '<EMAIL>',
          avatar: 'JS'
        },
        doctor: {
          id: 1,
          name: 'Dr. Sarah Johnson',
          specialty: 'Cardiology',
          avatar: 'SJ'
        },
        date: '2023-05-15',
        diagnosis: 'Common cold',
        symptoms: ['Runny nose', 'Sore throat', 'Mild fever'],
        treatment: 'Rest and fluids',
        medications: [
          { name: 'Acetaminophen', dosage: '500mg', frequency: 'Every 6 hours as needed' },
          { name: 'Cough syrup', dosage: '10ml', frequency: 'Every 8 hours as needed' }
        ],
        notes: 'Patient should recover within a week. Follow up if symptoms persist beyond 7 days.',
        followUpNeeded: true,
        followUpDate: '2023-05-22'
      };
      
      // Mock medical images
      const mockImages = [
        {
          id: 1,
          title: 'Throat Examination',
          date: '2023-05-15',
          imageUrl: 'https://source.unsplash.com/random/800x600/?medical',
          description: 'Throat examination showing mild inflammation consistent with common cold.',
          type: 'Clinical Photo'
        },
        {
          id: 2,
          title: 'Temperature Reading',
          date: '2023-05-15',
          imageUrl: 'https://source.unsplash.com/random/800x600/?thermometer',
          description: 'Temperature reading showing mild fever (100.2°F).',
          type: 'Clinical Data'
        },
        {
          id: 3,
          title: 'Prescription',
          date: '2023-05-15',
          imageUrl: 'https://source.unsplash.com/random/800x600/?prescription',
          description: 'Prescription for medications to treat common cold symptoms.',
          type: 'Document'
        }
      ];
      
      setMedicalRecord(mockMedicalRecord);
      setMedicalImages(mockImages);
      setLoading(false);
    }, 500);
  }, [id]);
  
  // Generate avatar text from name
  const generateAvatar = (name) => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return `${nameParts[0][0]}${nameParts[1][0]}`;
    }
    return name.substring(0, 2).toUpperCase();
  };
  
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Typography>Loading medical record details...</Typography>
        </Container>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Medical Record Details
          </Typography>
          <Box>
            <Button 
              variant="outlined" 
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate(`/admin/students/${medicalRecord.student.id}`)}
              sx={{ borderRadius: 2, mr: 2 }}
            >
              Back to Student
            </Button>
            <Button 
              variant="contained" 
              startIcon={<EditIcon />}
              onClick={() => navigate(`/admin/medical-records/${id}/edit`)}
              sx={{ borderRadius: 2 }}
            >
              Edit Record
            </Button>
          </Box>
        </Box>
        
        {/* Medical Record Details */}
        <Grid container spacing={4}>
          {/* Left Column - Medical Info */}
          <Grid item xs={12} md={7}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                height: '100%'
              }}
            >
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Medical Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <List disablePadding>
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <EventIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Date" 
                    secondary={medicalRecord.date}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <MedicalInformationIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Diagnosis" 
                    secondary={medicalRecord.diagnosis}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
                  />
                </ListItem>
                
                <ListItem disableGutters alignItems="flex-start">
                  <ListItemIcon sx={{ minWidth: 40, mt: 0.5 }}>
                    <MedicalInformationIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Symptoms" 
                    secondary={
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                        {medicalRecord.symptoms.map((symptom, index) => (
                          <Chip 
                            key={index} 
                            label={symptom} 
                            size="small" 
                            variant="outlined"
                          />
                        ))}
                      </Box>
                    }
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                  />
                </ListItem>
                
                <ListItem disableGutters>
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <HealingIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Treatment" 
                    secondary={medicalRecord.treatment}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
                
                <ListItem disableGutters alignItems="flex-start">
                  <ListItemIcon sx={{ minWidth: 40, mt: 0.5 }}>
                    <MedicalInformationIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Medications" 
                    secondary={
                      <List dense disablePadding>
                        {medicalRecord.medications.map((medication, index) => (
                          <ListItem key={index} disablePadding sx={{ mt: 1 }}>
                            <ListItemText 
                              primary={medication.name}
                              secondary={`${medication.dosage}, ${medication.frequency}`}
                              primaryTypographyProps={{ variant: 'body2', fontWeight: 'medium' }}
                              secondaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    }
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                  />
                </ListItem>
                
                <ListItem disableGutters alignItems="flex-start">
                  <ListItemIcon sx={{ minWidth: 40, mt: 0.5 }}>
                    <NoteIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Notes" 
                    secondary={medicalRecord.notes || 'No notes provided'}
                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    secondaryTypographyProps={{ variant: 'body1' }}
                  />
                </ListItem>
                
                {medicalRecord.followUpNeeded && (
                  <ListItem disableGutters>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <EventIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Follow-up Date" 
                      secondary={medicalRecord.followUpDate}
                      primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                      secondaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
                    />
                  </ListItem>
                )}
              </List>
            </Paper>
          </Grid>
          
          {/* Right Column - People Involved */}
          <Grid item xs={12} md={5}>
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                mb: 4
              }}
            >
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Student
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar 
                  sx={{ 
                    bgcolor: 'secondary.main',
                    width: 50,
                    height: 50,
                    mr: 2
                  }}
                >
                  {generateAvatar(medicalRecord.student.name)}
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" fontWeight="medium">
                    {medicalRecord.student.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {medicalRecord.student.email}
                  </Typography>
                </Box>
              </Box>
              
              <Button 
                variant="outlined" 
                size="small"
                onClick={() => navigate(`/admin/students/${medicalRecord.student.id}`)}
                sx={{ borderRadius: 2 }}
              >
                View Student Profile
              </Button>
            </Paper>
            
            <Paper 
              sx={{ 
                p: 3, 
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
              }}
            >
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Doctor
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar 
                  sx={{ 
                    bgcolor: 'primary.main',
                    width: 50,
                    height: 50,
                    mr: 2
                  }}
                >
                  {generateAvatar(medicalRecord.doctor.name)}
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" fontWeight="medium">
                    {medicalRecord.doctor.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {medicalRecord.doctor.specialty}
                  </Typography>
                </Box>
              </Box>
              
              <Button 
                variant="outlined" 
                size="small"
                onClick={() => navigate(`/admin/doctors/${medicalRecord.doctor.id}`)}
                sx={{ borderRadius: 2 }}
              >
                View Doctor Profile
              </Button>
            </Paper>
          </Grid>
        </Grid>
        
        {/* Images Tab Section */}
        <Paper 
          sx={{ 
            mt: 4, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden'
          }}
        >
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange}
              aria-label="medical record tabs"
            >
              <Tab 
                icon={<ImageIcon />} 
                iconPosition="start" 
                label="Images & Documents" 
                id="tab-0" 
                aria-controls="tabpanel-0" 
              />
            </Tabs>
          </Box>
          
          <Box
            role="tabpanel"
            hidden={tabValue !== 0}
            id="tabpanel-0"
            aria-labelledby="tab-0"
            sx={{ p: 3 }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6" fontWeight="medium">
                Medical Images & Documents
              </Typography>
              <Button 
                variant="outlined" 
                size="small"
                sx={{ borderRadius: 2 }}
              >
                Upload New Image
              </Button>
            </Box>
            
            <MedicalRecordImages 
              images={medicalImages} 
              variant="quilted"
              cols={4}
            />
          </Box>
        </Paper>
      </Container>
    </Layout>
  );
};

export default AdminMedicalRecordDetail;
