import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import VisibilityIcon from '@mui/icons-material/Visibility';
import EditIcon from '@mui/icons-material/Edit';
import MoreVertIcon from '@mui/icons-material/MoreVert';

const AdminMedicalRecordsList = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [records, setRecords] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [actionAnchorEl, setActionAnchorEl] = useState(null);
  const [selectedRecordId, setSelectedRecordId] = useState(null);
  
  useEffect(() => {
    // In a real app, you would fetch the medical records data from an API
    setLoading(true);
    setTimeout(() => {
      // Mock medical records data
      const mockRecords = [
        { id: 1, date: '2023-05-15', student: { id: 1, name: 'John Smith' }, doctor: { id: 1, name: 'Dr. Sarah Johnson' }, diagnosis: 'Common cold', followUpNeeded: true, followUpDate: '2023-05-22' },
        { id: 2, date: '2023-04-10', student: { id: 2, name: 'Emma Davis' }, doctor: { id: 1, name: 'Dr. Sarah Johnson' }, diagnosis: 'Anxiety', followUpNeeded: true, followUpDate: '2023-05-01' },
        { id: 3, date: '2023-05-05', student: { id: 3, name: 'Alex Rodriguez' }, doctor: { id: 2, name: 'Dr. Michael Chen' }, diagnosis: 'Sprained ankle', followUpNeeded: true, followUpDate: '2023-05-19' },
        { id: 4, date: '2023-05-12', student: { id: 1, name: 'John Smith' }, doctor: { id: 3, name: 'Dr. Emily Wilson' }, diagnosis: 'Insomnia', followUpNeeded: false, followUpDate: null },
        { id: 5, date: '2023-03-22', student: { id: 4, name: 'Sophia Lee' }, doctor: { id: 2, name: 'Dr. Michael Chen' }, diagnosis: 'Allergic reaction', followUpNeeded: false, followUpDate: null },
      ];
      
      setRecords(mockRecords);
      setLoading(false);
    }, 500);
  }, []);
  
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };
  
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
    setPage(0);
  };
  
  const handleFilterClick = (event) => {
    setFilterAnchorEl(event.currentTarget);
  };
  
  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };
  
  const handleActionClick = (event, recordId) => {
    setActionAnchorEl(event.currentTarget);
    setSelectedRecordId(recordId);
  };
  
  const handleActionClose = () => {
    setActionAnchorEl(null);
    setSelectedRecordId(null);
  };
  
  const handleViewRecord = () => {
    navigate(`/admin/medical-records/${selectedRecordId}`);
    handleActionClose();
  };
  
  const handleEditRecord = () => {
    navigate(`/admin/medical-records/${selectedRecordId}/edit`);
    handleActionClose();
  };
  
  // Filter records based on search query
  const filteredRecords = records.filter((record) => {
    const searchTerm = searchQuery.toLowerCase();
    return (
      record.student.name.toLowerCase().includes(searchTerm) ||
      record.doctor.name.toLowerCase().includes(searchTerm) ||
      record.diagnosis.toLowerCase().includes(searchTerm) ||
      record.date.includes(searchTerm)
    );
  });
  
  // Pagination
  const paginatedRecords = filteredRecords.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );
  
  // Check if a follow-up is overdue
  const isFollowUpOverdue = (followUpDate) => {
    if (!followUpDate) return false;
    const today = new Date();
    const followUp = new Date(followUpDate);
    return followUp < today;
  };
  
  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Medical Records
          </Typography>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={() => navigate('/admin/medical-records/new')}
            sx={{ borderRadius: 2 }}
          >
            New Medical Record
          </Button>
        </Box>
        
        {/* Search and Filter */}
        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
          <TextField
            variant="outlined"
            placeholder="Search records..."
            value={searchQuery}
            onChange={handleSearchChange}
            sx={{ flexGrow: 1, mr: 2 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={handleFilterClick}
            sx={{ borderRadius: 2 }}
          >
            Filter
          </Button>
          
          {/* Filter Menu */}
          <Menu
            anchorEl={filterAnchorEl}
            open={Boolean(filterAnchorEl)}
            onClose={handleFilterClose}
          >
            <MenuItem onClick={handleFilterClose}>
              All Records
            </MenuItem>
            <MenuItem onClick={handleFilterClose}>
              Records with Follow-up
            </MenuItem>
            <MenuItem onClick={handleFilterClose}>
              Overdue Follow-ups
            </MenuItem>
            <MenuItem onClick={handleFilterClose}>
              Records from Last Month
            </MenuItem>
          </Menu>
        </Box>
        
        {/* Records Table */}
        <Paper 
          sx={{ 
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: 'rgba(0,0,0,0.03)' }}>
                  <TableCell>Date</TableCell>
                  <TableCell>Student</TableCell>
                  <TableCell>Doctor</TableCell>
                  <TableCell>Diagnosis</TableCell>
                  <TableCell>Follow-up</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                      Loading medical records...
                    </TableCell>
                  </TableRow>
                ) : paginatedRecords.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                      No medical records found.
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedRecords.map((record) => (
                    <TableRow key={record.id} hover>
                      <TableCell>{record.date}</TableCell>
                      <TableCell>
                        <Box 
                          sx={{ 
                            color: 'primary.main', 
                            cursor: 'pointer',
                            '&:hover': { textDecoration: 'underline' }
                          }}
                          onClick={() => navigate(`/admin/students/${record.student.id}`)}
                        >
                          {record.student.name}
                        </Box>
                      </TableCell>
                      <TableCell>{record.doctor.name}</TableCell>
                      <TableCell>{record.diagnosis}</TableCell>
                      <TableCell>
                        {record.followUpNeeded ? (
                          <Chip 
                            label={record.followUpDate}
                            color={isFollowUpOverdue(record.followUpDate) ? "error" : "primary"}
                            size="small"
                          />
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            None
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell align="right">
                        <IconButton 
                          size="small" 
                          onClick={(e) => handleActionClick(e, record.id)}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            component="div"
            count={filteredRecords.length}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 25]}
          />
        </Paper>
        
        {/* Action Menu */}
        <Menu
          anchorEl={actionAnchorEl}
          open={Boolean(actionAnchorEl)}
          onClose={handleActionClose}
        >
          <MenuItem onClick={handleViewRecord}>
            <ListItemIcon>
              <VisibilityIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>View Details</ListItemText>
          </MenuItem>
          <MenuItem onClick={handleEditRecord}>
            <ListItemIcon>
              <EditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Edit Record</ListItemText>
          </MenuItem>
        </Menu>
      </Container>
    </Layout>
  );
};

export default AdminMedicalRecordsList;