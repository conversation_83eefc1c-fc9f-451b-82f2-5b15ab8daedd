import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Divider,
  Snackbar,
  Alert,
  Autocomplete,
  Chip,
  FormHelperText
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SaveIcon from '@mui/icons-material/Save';
import EventIcon from '@mui/icons-material/Event';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

const AdminAppointmentForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditMode = Boolean(id);
  
  const [formData, setFormData] = useState({
    student: null,
    doctor: null,
    date: '',
    time: '',
    duration: 30,
    type: '',
    status: 'scheduled',
    notes: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  
  // Mock data
  const appointmentTypes = ['General Checkup', 'Follow-up', 'Consultation', 'Emergency', 'Vaccination', 'Mental Health'];
  
  // Mock students data
  const students = [
    { id: 1, name: 'John Smith', email: '<EMAIL>', department: 'Computer Science' },
    { id: 2, name: 'Emma Davis', email: '<EMAIL>', department: 'Psychology' },
    { id: 3, name: 'Alex Rodriguez', email: '<EMAIL>', department: 'Business' },
    { id: 4, name: 'Sophia Martinez', email: '<EMAIL>', department: 'Medicine' },
    { id: 5, name: 'Daniel Lee', email: '<EMAIL>', department: 'Engineering' }
  ];
  
  // Mock doctors data
  const doctors = [
    { id: 1, name: 'Dr. Sarah Johnson', specialty: 'Cardiology' },
    { id: 2, name: 'Dr. Michael Chen', specialty: 'Psychiatry' },
    { id: 3, name: 'Dr. Emily Wilson', specialty: 'General Medicine' },
    { id: 4, name: 'Dr. Robert Brown', specialty: 'Orthopedics' },
    { id: 5, name: 'Dr. Lisa Garcia', specialty: 'Dermatology' }
  ];
  
  useEffect(() => {
    if (isEditMode) {
      // In a real app, you would fetch the appointment data from an API
      setLoading(true);
      setTimeout(() => {
        // Mock appointment data
        const mockAppointment = {
          id: parseInt(id),
          student: students[0],
          doctor: doctors[0],
          date: '2023-06-15',
          time: '14:30',
          duration: 45,
          type: 'Follow-up',
          status: 'scheduled',
          notes: 'Follow-up appointment for previous treatment.'
        };
        
        setFormData({
          student: mockAppointment.student,
          doctor: mockAppointment.doctor,
          date: mockAppointment.date,
          time: mockAppointment.time,
          duration: mockAppointment.duration,
          type: mockAppointment.type,
          status: mockAppointment.status,
          notes: mockAppointment.notes
        });
        
        setLoading(false);
      }, 500);
    }
  }, [id, isEditMode]);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleAutocompleteChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.student || !formData.doctor || !formData.date || !formData.time || !formData.type) {
      setSnackbarMessage('Please fill in all required fields');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }
    
    setLoading(true);
    
    // In a real app, you would send the data to an API
    setTimeout(() => {
      setLoading(false);
      setSnackbarMessage(isEditMode ? 'Appointment updated successfully!' : 'Appointment scheduled successfully!');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
      
      // Navigate back to appointments list after a short delay
      setTimeout(() => {
        navigate('/admin/appointments');
      }, 1500);
    }, 1000);
  };
  
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };
  
  return (
    <Layout>
      <Container maxWidth="md" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            {isEditMode ? 'Edit Appointment' : 'Schedule New Appointment'}
          </Typography>
          <Button 
            variant="outlined" 
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/admin/appointments')}
            sx={{ borderRadius: 2 }}
          >
            Back to Appointments
          </Button>
        </Box>
        
        {/* Form */}
        <Paper 
          component="form"
          onSubmit={handleSubmit}
          sx={{ 
            p: 4, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Grid container spacing={3}>
            {/* Appointment Participants */}
            <Grid item xs={12}>
              <Typography variant="h6" fontWeight="medium" gutterBottom>
                Appointment Participants
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={students}
                getOptionLabel={(option) => option.name}
                value={formData.student}
                onChange={(event, newValue) => {
                  handleAutocompleteChange('student', newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Student"
                    required
                    fullWidth
                    disabled={loading}
                  />
                )}
                renderOption={(props, option) => (
                  <li {...props}>
                    <Box>
                      <Typography variant="body1">{option.name}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {option.email} • {option.department}
                      </Typography>
                    </Box>
                  </li>
                )}
                disabled={loading}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={doctors}
                getOptionLabel={(option) => option.name}
                value={formData.doctor}
                onChange={(event, newValue) => {
                  handleAutocompleteChange('doctor', newValue);
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Doctor"
                    required
                    fullWidth
                    disabled={loading}
                  />
                )}
                renderOption={(props, option) => (
                  <li {...props}>
                    <Box>
                      <Typography variant="body1">{option.name}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {option.specialty}
                      </Typography>
                    </Box>
                  </li>
                )}
                disabled={loading}
              />
            </Grid>
            
            {/* Appointment Details */}
            <Grid item xs={12}>
              <Typography variant="h6" fontWeight="medium" gutterBottom sx={{ mt: 2 }}>
                Appointment Details
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Date"
                name="date"
                type="date"
                value={formData.date}
                onChange={handleChange}
                disabled={loading}
                InputLabelProps={{
                  shrink: true,
                }}
                InputProps={{
                  startAdornment: (
                    <EventIcon color="action" sx={{ mr: 1 }} />
                  ),
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                required
                label="Time"
                name="time"
                type="time"
                value={formData.time}
                onChange={handleChange}
                disabled={loading}
                InputLabelProps={{
                  shrink: true,
                }}
                InputProps={{
                  startAdornment: (
                    <AccessTimeIcon color="action" sx={{ mr: 1 }} />
                  ),
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Duration (minutes)</InputLabel>
                <Select
                  name="duration"
                  value={formData.duration}
                  onChange={handleChange}
                  label="Duration (minutes)"
                  disabled={loading}
                >
                  <MenuItem value={15}>15 minutes</MenuItem>
                  <MenuItem value={30}>30 minutes</MenuItem>
                  <MenuItem value={45}>45 minutes</MenuItem>
                  <MenuItem value={60}>60 minutes</MenuItem>
                  <MenuItem value={90}>90 minutes</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Appointment Type</InputLabel>
                <Select
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  label="Appointment Type"
                  disabled={loading}
                >
                  {appointmentTypes.map((type) => (
                    <MenuItem key={type} value={type}>{type}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            {isEditMode && (
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Status</InputLabel>
                  <Select
                    name="status"
                    value={formData.status}
                    onChange={handleChange}
                    label="Status"
                    disabled={loading}
                  >
                    <MenuItem value="scheduled">Scheduled</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                    <MenuItem value="cancelled">Cancelled</MenuItem>
                    <MenuItem value="no-show">No Show</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                name="notes"
                multiline
                rows={4}
                value={formData.notes}
                onChange={handleChange}
                disabled={loading}
                placeholder="Enter any additional information or special instructions for this appointment"
              />
            </Grid>
            
            {/* Submit Button */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                size="large"
                startIcon={<SaveIcon />}
                disabled={loading}
                sx={{ 
                  borderRadius: 2,
                  px: 4,
                  py: 1.5
                }}
              >
                {loading ? 'Saving...' : isEditMode ? 'Update Appointment' : 'Schedule Appointment'}
              </Button>
            </Grid>
          </Grid>
        </Paper>
      </Container>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbarSeverity} 
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default AdminAppointmentForm;