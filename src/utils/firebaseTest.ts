import { auth } from '../services/firebase';

export const testFirebaseConnection = async () => {
  try {
    console.log('Testing Firebase connection...');
    console.log('Auth instance:', auth);
    console.log('Auth app:', auth.app);
    console.log('Auth config:', auth.config);
    
    // Test if we can access the auth service
    const currentUser = auth.currentUser;
    console.log('Current user:', currentUser);
    
    return {
      success: true,
      message: 'Firebase connection successful'
    };
  } catch (error) {
    console.error('Firebase connection test failed:', error);
    return {
      success: false,
      error: error
    };
  }
};
