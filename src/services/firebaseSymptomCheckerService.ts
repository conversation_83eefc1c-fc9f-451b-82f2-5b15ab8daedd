import { getAllSymptoms, getAllConditions, type Symptom, type Condition } from './symptomConditionService';

// Define types for our data
export interface DiagnosisResult {
  conditions: ConditionResult[];
  triage: {
    triage_level: string;
    serious: string[];
  };
}

export interface ConditionResult {
  id: string;
  name: string;
  common_name?: string;
  probability: number;
  probability_display: string;
  probability_percentage: number;
  probability_label: string;
  triage_level: string;
  description: string;
  treatment?: string;
  risk_factors?: string[];
}

// Emergency symptoms that require immediate attention
const EMERGENCY_SYMPTOMS = [
  's_57', // Coughing Blood
  's_59', // Bluish Lips/Fingernails
  's_111', // Severe <PERSON>ache (Sudden Onset)
  's_112', // Stiff Neck (Neurological)
  's_117', // Facial Droop
  's_118', // One-Sided Weakness
  's_119', // Vision Changes (Sudden)
  's_120', // Loss of Consciousness
  's_121', // Seizures
  's_180', // Vision Loss (Sudden)
  's_183', // Hearing Loss (Sudden)
];

// Helper function to safely calculate specificity
const calculateSpecificity = (matchCount: number, selectedSymptoms: string[]): number => {
  if (!selectedSymptoms || selectedSymptoms.length === 0) {
    return 0;
  }
  const specificity = matchCount / selectedSymptoms.length;
  return isNaN(specificity) ? 0 : Math.min(1, Math.max(0, specificity));
};

// Helper function to ensure probability calculations are safe
const safeProbabilityCalculation = (result: number): number => {
  if (isNaN(result) || !isFinite(result)) {
    return 0;
  }
  return Math.min(1, Math.max(0, result));
};

// Function to evaluate probability function string
const evaluateProbabilityFunction = (
  functionString: string,
  matchCount: number,
  selectedSymptoms: string[],
  userData?: { age?: number; sex?: string }
): number => {
  try {
    // Create a safe evaluation context
    const context = {
      matchCount,
      selectedSymptoms,
      userData,
      calculateSpecificity,
      safeProbabilityCalculation,
      Math,
      isNaN,
      isFinite
    };

    // Create function from string and execute it
    const func = new Function(
      'matchCount',
      'selectedSymptoms', 
      'userData',
      'calculateSpecificity',
      'safeProbabilityCalculation',
      'Math',
      'isNaN',
      'isFinite',
      `return (${functionString})(matchCount, selectedSymptoms, userData);`
    );

    const result = func(
      matchCount,
      selectedSymptoms,
      userData,
      calculateSpecificity,
      safeProbabilityCalculation,
      Math,
      isNaN,
      isFinite
    );

    return safeProbabilityCalculation(result);
  } catch (error) {
    console.error('Error evaluating probability function:', error);
    // Fallback to simple calculation
    return safeProbabilityCalculation(matchCount / selectedSymptoms.length);
  }
};

/**
 * Get all symptoms from Firebase
 */
export const getSymptoms = async (): Promise<Symptom[]> => {
  try {
    const symptoms = await getAllSymptoms();
    console.log(`📋 Retrieved ${symptoms.length} symptoms from Firebase`);
    return symptoms;
  } catch (error) {
    console.error('❌ Error fetching symptoms from Firebase:', error);
    throw new Error('Unable to retrieve symptoms. Please try again later.');
  }
};

/**
 * Get diagnosis based on selected symptoms using Firebase data
 */
export const getDiagnosis = async (
  selectedSymptoms: string[],
  sex: string,
  age: number
): Promise<DiagnosisResult> => {
  try {
    if (!selectedSymptoms || selectedSymptoms.length === 0) {
      throw new Error('No symptoms provided');
    }

    console.log(`🔍 Analyzing ${selectedSymptoms.length} symptoms for ${sex}, age ${age}`);

    // Get all conditions from Firebase
    const conditions = await getAllConditions();
    
    if (conditions.length === 0) {
      throw new Error('No medical conditions available. Please contact support.');
    }

    console.log(`🏥 Evaluating against ${conditions.length} conditions`);

    const userData = { age, sex };
    const conditionResults: ConditionResult[] = [];

    // Check for emergency symptoms
    const hasEmergencySymptoms = selectedSymptoms.some(symptom => 
      EMERGENCY_SYMPTOMS.includes(symptom)
    );

    let overallTriageLevel = 'self_care';
    const seriousMessages: string[] = [];

    // Evaluate each condition
    for (const condition of conditions) {
      try {
        // Count matching symptoms
        const matchingSymptoms = condition.symptoms.filter(symptom => 
          selectedSymptoms.includes(symptom)
        );
        const matchCount = matchingSymptoms.length;

        if (matchCount === 0) continue; // Skip conditions with no matching symptoms

        // Calculate probability using the stored function
        let probability = 0;
        if (condition.probability_function) {
          probability = evaluateProbabilityFunction(
            condition.probability_function,
            matchCount,
            selectedSymptoms,
            userData
          );
        } else {
          // Fallback calculation
          probability = safeProbabilityCalculation(matchCount / condition.symptoms.length);
        }

        // Skip very low probability conditions
        if (probability < 0.05) continue;

        // Determine probability label
        let probabilityLabel = 'Low';
        if (probability >= 0.7) probabilityLabel = 'High';
        else if (probability >= 0.4) probabilityLabel = 'Medium';

        // Update overall triage level
        if (condition.triage_level === 'emergency' && probability > 0.3) {
          overallTriageLevel = 'emergency';
          seriousMessages.push(`${condition.name} requires immediate medical attention`);
        } else if (condition.triage_level === 'consultation' && probability > 0.4 && overallTriageLevel !== 'emergency') {
          overallTriageLevel = 'consultation';
        }

        conditionResults.push({
          id: condition.id,
          name: condition.name,
          common_name: condition.name,
          probability,
          probability_display: `${Math.round(probability * 100)}%`,
          probability_percentage: Math.round(probability * 100),
          probability_label: probabilityLabel,
          triage_level: condition.triage_level,
          description: condition.description,
          treatment: condition.treatment,
          risk_factors: condition.risk_factors
        });
      } catch (conditionError) {
        console.error(`Error evaluating condition ${condition.name}:`, conditionError);
        continue;
      }
    }

    // Sort by probability (highest first)
    conditionResults.sort((a, b) => b.probability - a.probability);

    // Override triage level if emergency symptoms are present
    if (hasEmergencySymptoms) {
      overallTriageLevel = 'emergency';
      seriousMessages.push('Emergency symptoms detected - seek immediate medical attention');
    }

    console.log(`✅ Analysis complete: ${conditionResults.length} potential conditions, triage level: ${overallTriageLevel}`);

    return {
      conditions: conditionResults.slice(0, 10), // Return top 10 conditions
      triage: {
        triage_level: overallTriageLevel,
        serious: seriousMessages
      }
    };

  } catch (error) {
    console.error('❌ Error calculating diagnosis:', error);
    throw new Error('Unable to calculate diagnosis. Please try again later.');
  }
};

// Export service object for compatibility
export const firebaseSymptomCheckerService = {
  getSymptoms,
  getDiagnosis
};

export default firebaseSymptomCheckerService;
