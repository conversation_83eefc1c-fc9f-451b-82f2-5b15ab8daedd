import axios from 'axios';

const API_URL = 'https://api.infermedica.com/v3';
const API_KEY = import.meta.env.VITE_INFERMEDICA_API_KEY;
const API_APP_ID = import.meta.env.VITE_INFERMEDICA_APP_ID;

const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'App-Id': API_APP_ID,
    'App-Key': API_KEY,
    'Content-Type': 'application/json'
  }
});

export const getSymptoms = async () => {
  try {
    const response = await apiClient.get('/symptoms');
    return response.data;
  } catch (error) {
    console.error('Error fetching symptoms:', error);
    throw error;
  }
};

export const getDiagnosis = async (symptoms: string[], sex: string, age: number) => {
  try {
    const response = await apiClient.post('/diagnosis', {
      sex,
      age,
      evidence: symptoms.map(symptom => ({
        id: symptom,
        choice_id: 'present',
        source: 'initial'
      }))
    });
    return response.data;
  } catch (error) {
    console.error('Error getting diagnosis:', error);
    throw error;
  }
};

export default {
  getSymptoms,
  getDiagnosis
};