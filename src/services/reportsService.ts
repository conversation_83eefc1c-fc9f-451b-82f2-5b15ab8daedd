import { getAllUsers } from './authService';
import { getAllAppointments, getAppointmentStats } from './appointmentService';
import { getAllMedicalRecords, getCommonDiagnoses, getMedicalRecordStats } from './medicalRecordService';
import { getDoctorsFromFirebase } from './doctorAssignmentService';

export interface ReportData {
  id: string;
  name: string;
  description: string;
  category: 'users' | 'appointments' | 'medical' | 'system';
  data: any;
  generatedAt: Date;
  generatedBy: string;
}

export interface UserReport {
  totalUsers: number;
  usersByRole: { role: string; count: number }[];
  newUsersThisMonth: number;
  activeUsers: number;
  userGrowthTrend: { month: string; count: number }[];
}

export interface AppointmentReport {
  totalAppointments: number;
  appointmentsByStatus: { status: string; count: number }[];
  appointmentsByType: { type: string; count: number }[];
  appointmentsThisMonth: number;
  averageAppointmentsPerDay: number;
  busyDoctors: { name: string; appointmentCount: number }[];
}

export interface MedicalReport {
  totalRecords: number;
  recordsThisMonth: number;
  commonDiagnoses: { name: string; count: number }[];
  followUpsPending: number;
  recordsByDoctor: { doctorName: string; recordCount: number }[];
  treatmentOutcomes: { status: string; count: number }[];
}

export interface SystemReport {
  systemUptime: string;
  totalStorage: string;
  activeConnections: number;
  errorRate: number;
  performanceMetrics: { metric: string; value: string }[];
}

/**
 * Generate user analytics report
 */
export const generateUserReport = async (): Promise<UserReport> => {
  try {
    const users = await getAllUsers();
    
    // Count users by role
    const usersByRole = users.reduce((acc, user) => {
      const role = user.role || 'student';
      const existing = acc.find(item => item.role === role);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ role, count: 1 });
      }
      return acc;
    }, [] as { role: string; count: number }[]);

    // Count new users this month
    const thisMonth = new Date();
    thisMonth.setDate(1);
    const newUsersThisMonth = users.filter(user => {
      const userDate = user.createdAt;
      if (!userDate) return false;
      const date = (userDate as any).seconds ? 
        new Date((userDate as any).seconds * 1000) : 
        new Date(userDate);
      return date >= thisMonth;
    }).length;

    // Generate growth trend (last 6 months)
    const userGrowthTrend = [];
    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date();
      monthDate.setMonth(monthDate.getMonth() - i);
      monthDate.setDate(1);
      
      const nextMonth = new Date(monthDate);
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      
      const usersInMonth = users.filter(user => {
        const userDate = user.createdAt;
        if (!userDate) return false;
        const date = (userDate as any).seconds ? 
          new Date((userDate as any).seconds * 1000) : 
          new Date(userDate);
        return date >= monthDate && date < nextMonth;
      }).length;

      userGrowthTrend.push({
        month: monthDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        count: usersInMonth
      });
    }

    return {
      totalUsers: users.length,
      usersByRole,
      newUsersThisMonth,
      activeUsers: users.length, // Assuming all users are active for now
      userGrowthTrend
    };
  } catch (error) {
    console.error('❌ Error generating user report:', error);
    throw error;
  }
};

/**
 * Generate appointment analytics report
 */
export const generateAppointmentReport = async (): Promise<AppointmentReport> => {
  try {
    const appointments = await getAllAppointments();
    const stats = await getAppointmentStats();
    
    // Count appointments by status
    const appointmentsByStatus = appointments.reduce((acc, apt) => {
      const existing = acc.find(item => item.status === apt.status);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ status: apt.status, count: 1 });
      }
      return acc;
    }, [] as { status: string; count: number }[]);

    // Count appointments by type
    const appointmentsByType = appointments.reduce((acc, apt) => {
      const existing = acc.find(item => item.type === apt.type);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ type: apt.type, count: 1 });
      }
      return acc;
    }, [] as { type: string; count: number }[]);

    // Find busy doctors
    const doctorAppointments = appointments.reduce((acc, apt) => {
      const existing = acc.find(item => item.name === apt.doctorName);
      if (existing) {
        existing.appointmentCount++;
      } else {
        acc.push({ name: apt.doctorName, appointmentCount: 1 });
      }
      return acc;
    }, [] as { name: string; appointmentCount: number }[]);

    const busyDoctors = doctorAppointments
      .sort((a, b) => b.appointmentCount - a.appointmentCount)
      .slice(0, 5);

    // Calculate average appointments per day
    const daysWithAppointments = new Set(appointments.map(apt => apt.date)).size;
    const averageAppointmentsPerDay = daysWithAppointments > 0 ? 
      Math.round(appointments.length / daysWithAppointments) : 0;

    return {
      totalAppointments: appointments.length,
      appointmentsByStatus,
      appointmentsByType,
      appointmentsThisMonth: stats.thisMonth,
      averageAppointmentsPerDay,
      busyDoctors
    };
  } catch (error) {
    console.error('❌ Error generating appointment report:', error);
    throw error;
  }
};

/**
 * Generate medical records analytics report
 */
export const generateMedicalReport = async (): Promise<MedicalReport> => {
  try {
    const records = await getAllMedicalRecords();
    const commonDiagnoses = await getCommonDiagnoses();
    const stats = await getMedicalRecordStats();
    
    // Count records by doctor
    const recordsByDoctor = records.reduce((acc, record) => {
      const existing = acc.find(item => item.doctorName === record.doctorName);
      if (existing) {
        existing.recordCount++;
      } else {
        acc.push({ doctorName: record.doctorName, recordCount: 1 });
      }
      return acc;
    }, [] as { doctorName: string; recordCount: number }[]);

    // Count treatment outcomes
    const treatmentOutcomes = records.reduce((acc, record) => {
      const existing = acc.find(item => item.status === record.status);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ status: record.status, count: 1 });
      }
      return acc;
    }, [] as { status: string; count: number }[]);

    // Count pending follow-ups
    const followUpsPending = records.filter(record => 
      record.followUpRequired && 
      record.followUpDate && 
      new Date(record.followUpDate) > new Date()
    ).length;

    return {
      totalRecords: records.length,
      recordsThisMonth: stats.recordsThisMonth,
      commonDiagnoses: commonDiagnoses.slice(0, 10),
      followUpsPending,
      recordsByDoctor: recordsByDoctor.sort((a, b) => b.recordCount - a.recordCount).slice(0, 5),
      treatmentOutcomes
    };
  } catch (error) {
    console.error('❌ Error generating medical report:', error);
    throw error;
  }
};

/**
 * Generate system performance report
 */
export const generateSystemReport = async (): Promise<SystemReport> => {
  try {
    // Mock system metrics for now - in a real system, these would come from monitoring tools
    return {
      systemUptime: '99.9%',
      totalStorage: '2.4 GB',
      activeConnections: Math.floor(Math.random() * 100) + 50,
      errorRate: 0.1,
      performanceMetrics: [
        { metric: 'Average Response Time', value: '120ms' },
        { metric: 'Database Queries/sec', value: '45' },
        { metric: 'Memory Usage', value: '68%' },
        { metric: 'CPU Usage', value: '23%' },
        { metric: 'Disk Usage', value: '45%' }
      ]
    };
  } catch (error) {
    console.error('❌ Error generating system report:', error);
    throw error;
  }
};

/**
 * Generate comprehensive dashboard report
 */
export const generateDashboardReport = async () => {
  try {
    const [userReport, appointmentReport, medicalReport, systemReport] = await Promise.all([
      generateUserReport(),
      generateAppointmentReport(),
      generateMedicalReport(),
      generateSystemReport()
    ]);

    return {
      userReport,
      appointmentReport,
      medicalReport,
      systemReport,
      generatedAt: new Date()
    };
  } catch (error) {
    console.error('❌ Error generating dashboard report:', error);
    throw error;
  }
};

/**
 * Export report data as CSV
 */
export const exportReportAsCSV = (data: any[], filename: string): void => {
  if (data.length === 0) return;

  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
      }).join(',')
    )
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv' });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}_${new Date().toISOString().split('T')[0]}.csv`;
  link.click();
  window.URL.revokeObjectURL(url);
};
