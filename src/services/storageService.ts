import { ref, uploadBytesResumable, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from './firebase';
import { v4 as uuidv4 } from 'uuid';

export type FileCategory = 'medical_records' | 'lab_results' | 'prescriptions' | 'imaging';

export interface FileMetadata {
  description: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface UploadResult {
  id: string;
  url: string;
  path: string;
  metadata: Partial<FileMetadata>;
}

/**
 * Uploads a file to Firebase Storage
 * @param userId The user ID
 * @param file The file to upload
 * @param category The file category
 * @param metadata Additional metadata for the file
 * @returns Promise with the upload result
 */
export const uploadFile = async (
  userId: string,
  file: File,
  category: FileCategory,
  metadata: Partial<FileMetadata> = {}
): Promise<UploadResult> => {
  // Generate a unique ID for the file
  const fileId = uuidv4();
  
  // Create a storage reference
  const fileExtension = file.name.split('.').pop();
  const path = `users/${userId}/${category}/${fileId}.${fileExtension}`;
  const storageRef = ref(storage, path);
  
  // Upload the file
  const uploadTask = uploadBytesResumable(storageRef, file);
  
  // Return a promise that resolves when the upload is complete
  return new Promise((resolve, reject) => {
    uploadTask.on(
      'state_changed',
      (snapshot) => {
        // Progress updates are handled by the hook
      },
      (error) => {
        // Handle errors
        reject(error);
      },
      async () => {
        // Upload completed successfully
        try {
          const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
          
          // Return the result
          resolve({
            id: fileId,
            url: downloadURL,
            path: path,
            metadata: {
              ...metadata,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          });
        } catch (error) {
          reject(error);
        }
      }
    );
  });
};

/**
 * Deletes a file from Firebase Storage
 * @param userId The user ID
 * @param fileId The file ID
 * @param category The file category
 * @param path Optional direct path to the file
 * @returns Promise that resolves when the file is deleted
 */
export const deleteFile = async (
  userId: string,
  fileId: string,
  category: FileCategory,
  path?: string
): Promise<void> => {
  // If path is provided, use it directly
  const filePath = path || `users/${userId}/${category}/${fileId}`;
  const fileRef = ref(storage, filePath);
  
  // Delete the file
  return deleteObject(fileRef);
};

/**
 * Gets the download URL for a file
 * @param userId The user ID
 * @param fileId The file ID
 * @param category The file category
 * @param extension The file extension
 * @returns Promise with the download URL
 */
export const getFileUrl = async (
  userId: string,
  fileId: string,
  category: FileCategory,
  extension: string
): Promise<string> => {
  const path = `users/${userId}/${category}/${fileId}.${extension}`;
  const fileRef = ref(storage, path);
  return getDownloadURL(fileRef);
};
