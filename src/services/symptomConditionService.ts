import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  where,
  writeBatch,
  Timestamp 
} from 'firebase/firestore';
import { db } from './firebase';

export interface Symptom {
  id: string;
  name: string;
  category?: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Condition {
  id: string;
  name: string;
  symptoms: string[]; // Array of symptom IDs
  probability_function: string; // Stored as string, will be evaluated
  triage_level: 'self_care' | 'consultation' | 'emergency';
  description: string;
  treatment: string;
  risk_factors: string[];
  category?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Get all symptoms from Firebase
 */
export const getAllSymptoms = async (): Promise<Symptom[]> => {
  try {
    const querySnapshot = await getDocs(
      query(collection(db, 'symptoms'), orderBy('name', 'asc'))
    );
    
    const symptoms: Symptom[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      symptoms.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Symptom);
    });
    
    console.log(`📋 Retrieved ${symptoms.length} symptoms`);
    return symptoms;
  } catch (error) {
    console.error('❌ Error fetching symptoms:', error);
    return [];
  }
};

/**
 * Get all conditions from Firebase
 */
export const getAllConditions = async (): Promise<Condition[]> => {
  try {
    const querySnapshot = await getDocs(
      query(collection(db, 'conditions'), orderBy('name', 'asc'))
    );
    
    const conditions: Condition[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      conditions.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Condition);
    });
    
    console.log(`🏥 Retrieved ${conditions.length} conditions`);
    return conditions;
  } catch (error) {
    console.error('❌ Error fetching conditions:', error);
    return [];
  }
};

/**
 * Generate next available symptom ID
 */
export const getNextSymptomId = async (): Promise<string> => {
  try {
    const symptoms = await getAllSymptoms();
    const existingIds = symptoms.map(s => s.id).filter(id => id.startsWith('s_'));
    const numbers = existingIds.map(id => parseInt(id.replace('s_', ''))).filter(n => !isNaN(n));
    const maxNumber = numbers.length > 0 ? Math.max(...numbers) : 0;
    return `s_${maxNumber + 1}`;
  } catch (error) {
    console.error('❌ Error generating symptom ID:', error);
    return `s_${Date.now()}`;
  }
};

/**
 * Generate next available condition ID
 */
export const getNextConditionId = async (): Promise<string> => {
  try {
    const conditions = await getAllConditions();
    const existingIds = conditions.map(c => c.id).filter(id => id.startsWith('c_'));
    const numbers = existingIds.map(id => parseInt(id.replace('c_', ''))).filter(n => !isNaN(n));
    const maxNumber = numbers.length > 0 ? Math.max(...numbers) : 0;
    const nextNumber = maxNumber + 1;
    return `c_${nextNumber.toString().padStart(3, '0')}`;
  } catch (error) {
    console.error('❌ Error generating condition ID:', error);
    return `c_${Date.now()}`;
  }
};

/**
 * Check if symptom name already exists
 */
export const checkSymptomDuplicate = async (name: string, excludeId?: string): Promise<boolean> => {
  try {
    const symptoms = await getAllSymptoms();
    return symptoms.some(s =>
      s.name.toLowerCase() === name.toLowerCase() &&
      s.id !== excludeId
    );
  } catch (error) {
    console.error('❌ Error checking symptom duplicate:', error);
    return false;
  }
};

/**
 * Check if condition name already exists
 */
export const checkConditionDuplicate = async (name: string, excludeId?: string): Promise<boolean> => {
  try {
    const conditions = await getAllConditions();
    return conditions.some(c =>
      c.name.toLowerCase() === name.toLowerCase() &&
      c.id !== excludeId
    );
  } catch (error) {
    console.error('❌ Error checking condition duplicate:', error);
    return false;
  }
};

/**
 * Add a new symptom
 */
export const addSymptom = async (symptomData: Omit<Symptom, 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    // Check for duplicate name
    const isDuplicate = await checkSymptomDuplicate(symptomData.name);
    if (isDuplicate) {
      throw new Error(`Symptom "${symptomData.name}" already exists`);
    }

    const docRef = await addDoc(collection(db, 'symptoms'), {
      ...symptomData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
    console.log('✅ Symptom added with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('❌ Error adding symptom:', error);
    throw error;
  }
};

/**
 * Add a new condition
 */
export const addCondition = async (conditionData: Omit<Condition, 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    // Check for duplicate name
    const isDuplicate = await checkConditionDuplicate(conditionData.name);
    if (isDuplicate) {
      throw new Error(`Condition "${conditionData.name}" already exists`);
    }

    const docRef = await addDoc(collection(db, 'conditions'), {
      ...conditionData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
    console.log('✅ Condition added with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('❌ Error adding condition:', error);
    throw error;
  }
};

/**
 * Update a symptom
 */
export const updateSymptom = async (symptomId: string, updates: Partial<Symptom>): Promise<void> => {
  try {
    await updateDoc(doc(db, 'symptoms', symptomId), {
      ...updates,
      updatedAt: Timestamp.now()
    });
    console.log('✅ Symptom updated');
  } catch (error) {
    console.error('❌ Error updating symptom:', error);
    throw error;
  }
};

/**
 * Update a condition
 */
export const updateCondition = async (conditionId: string, updates: Partial<Condition>): Promise<void> => {
  try {
    await updateDoc(doc(db, 'conditions', conditionId), {
      ...updates,
      updatedAt: Timestamp.now()
    });
    console.log('✅ Condition updated');
  } catch (error) {
    console.error('❌ Error updating condition:', error);
    throw error;
  }
};

/**
 * Delete a symptom
 */
export const deleteSymptom = async (symptomId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'symptoms', symptomId));
    console.log('✅ Symptom deleted');
  } catch (error) {
    console.error('❌ Error deleting symptom:', error);
    throw error;
  }
};

/**
 * Delete a condition
 */
export const deleteCondition = async (conditionId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'conditions', conditionId));
    console.log('✅ Condition deleted');
  } catch (error) {
    console.error('❌ Error deleting condition:', error);
    throw error;
  }
};

/**
 * Bulk import symptoms from mock data
 */
export const importSymptomsFromMock = async (symptoms: any[]): Promise<void> => {
  try {
    const batch = writeBatch(db);
    
    symptoms.forEach((symptom) => {
      const docRef = doc(collection(db, 'symptoms'));
      batch.set(docRef, {
        id: symptom.id,
        name: symptom.name,
        category: symptom.category || 'General',
        description: symptom.description || '',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    });
    
    await batch.commit();
    console.log(`✅ Successfully imported ${symptoms.length} symptoms`);
  } catch (error) {
    console.error('❌ Error importing symptoms:', error);
    throw error;
  }
};

/**
 * Bulk import conditions from mock data
 */
export const importConditionsFromMock = async (conditions: any[]): Promise<void> => {
  try {
    const batch = writeBatch(db);
    
    conditions.forEach((condition) => {
      const docRef = doc(collection(db, 'conditions'));
      batch.set(docRef, {
        id: condition.id,
        name: condition.name,
        symptoms: condition.symptoms,
        probability_function: condition.probability_function.toString(),
        triage_level: condition.triage_level,
        description: condition.description,
        treatment: condition.treatment,
        risk_factors: condition.risk_factors,
        category: condition.category || 'General',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
    });
    
    await batch.commit();
    console.log(`✅ Successfully imported ${conditions.length} conditions`);
  } catch (error) {
    console.error('❌ Error importing conditions:', error);
    throw error;
  }
};

/**
 * Get symptoms by category
 */
export const getSymptomsByCategory = async (category: string): Promise<Symptom[]> => {
  try {
    const querySnapshot = await getDocs(
      query(
        collection(db, 'symptoms'),
        where('category', '==', category),
        orderBy('name', 'asc')
      )
    );
    
    const symptoms: Symptom[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      symptoms.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Symptom);
    });
    
    return symptoms;
  } catch (error) {
    console.error('❌ Error fetching symptoms by category:', error);
    return [];
  }
};

/**
 * Get conditions by triage level
 */
export const getConditionsByTriageLevel = async (triageLevel: string): Promise<Condition[]> => {
  try {
    const querySnapshot = await getDocs(
      query(
        collection(db, 'conditions'),
        where('triage_level', '==', triageLevel),
        orderBy('name', 'asc')
      )
    );
    
    const conditions: Condition[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      conditions.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Condition);
    });
    
    return conditions;
  } catch (error) {
    console.error('❌ Error fetching conditions by triage level:', error);
    return [];
  }
};

/**
 * Search symptoms by name
 */
export const searchSymptoms = async (searchTerm: string): Promise<Symptom[]> => {
  try {
    const allSymptoms = await getAllSymptoms();
    return allSymptoms.filter(symptom => 
      symptom.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  } catch (error) {
    console.error('❌ Error searching symptoms:', error);
    return [];
  }
};

/**
 * Search conditions by name
 */
export const searchConditions = async (searchTerm: string): Promise<Condition[]> => {
  try {
    const allConditions = await getAllConditions();
    return allConditions.filter(condition => 
      condition.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  } catch (error) {
    console.error('❌ Error searching conditions:', error);
    return [];
  }
};
