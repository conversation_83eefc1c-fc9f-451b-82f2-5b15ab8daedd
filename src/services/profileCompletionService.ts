// Profile Completion Detection Service
// Checks if student profile is complete and tracks completion status

import { doc, getDoc, setDoc, updateDoc } from 'firebase/firestore';
import { db } from './firebase';
import type { UserProfile, ProfileCompletionStatus, MedicalInformation, EmergencyContact } from '../types/firebase';

export interface ProfileCompletionResult {
  isComplete: boolean;
  personalDetailsComplete: boolean;
  medicalInfoComplete: boolean;
  emergencyContactComplete: boolean;
  missingFields: string[];
  completionPercentage: number;
}

/**
 * Check if personal details section is complete
 */
export const checkPersonalDetailsCompletion = (profile: UserProfile): { complete: boolean; missingFields: string[] } => {
  const requiredFields = [
    'firstName',
    'lastName',
    'dateOfBirth',
    'gender',
    'phoneNumber',
    'address',
    'city',
    'state',
    'zipCode',
    'department',
    'year',
    'major'
  ];
  
  const missingFields: string[] = [];
  
  requiredFields.forEach(field => {
    const value = profile[field as keyof UserProfile];
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      missingFields.push(field);
    }
  });
  
  return {
    complete: missingFields.length === 0,
    missingFields
  };
};

/**
 * Check if medical information section is complete
 */
export const checkMedicalInfoCompletion = (medicalInfo?: MedicalInformation): { complete: boolean; missingFields: string[] } => {
  if (!medicalInfo) {
    return {
      complete: false,
      missingFields: ['bloodType', 'allergies', 'medications', 'conditions']
    };
  }
  
  const missingFields: string[] = [];
  
  // Blood type is required
  if (!medicalInfo.bloodType || medicalInfo.bloodType.trim() === '') {
    missingFields.push('bloodType');
  }
  
  // Allergies array should be initialized (can be empty if no allergies)
  if (!Array.isArray(medicalInfo.allergies)) {
    missingFields.push('allergies');
  }
  
  // Medications array should be initialized (can be empty if no medications)
  if (!Array.isArray(medicalInfo.medications)) {
    missingFields.push('medications');
  }
  
  // Conditions array should be initialized (can be empty if no conditions)
  if (!Array.isArray(medicalInfo.conditions)) {
    missingFields.push('conditions');
  }
  
  return {
    complete: missingFields.length === 0,
    missingFields
  };
};

/**
 * Check if emergency contact section is complete
 */
export const checkEmergencyContactCompletion = (emergencyContact?: EmergencyContact): { complete: boolean; missingFields: string[] } => {
  if (!emergencyContact) {
    return {
      complete: false,
      missingFields: ['name', 'relationship', 'phone']
    };
  }
  
  const requiredFields = ['name', 'relationship', 'phone'];
  const missingFields: string[] = [];
  
  requiredFields.forEach(field => {
    const value = emergencyContact[field as keyof EmergencyContact];
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      missingFields.push(field);
    }
  });
  
  return {
    complete: missingFields.length === 0,
    missingFields
  };
};

/**
 * Check overall profile completion status
 */
export const checkProfileCompletion = (profile: UserProfile): ProfileCompletionResult => {
  const personalDetails = checkPersonalDetailsCompletion(profile);
  const medicalInfo = checkMedicalInfoCompletion(profile.medicalInfo);
  const emergencyContact = checkEmergencyContactCompletion(profile.emergencyContact);
  
  const allMissingFields = [
    ...personalDetails.missingFields.map(field => `personalDetails.${field}`),
    ...medicalInfo.missingFields.map(field => `medicalInfo.${field}`),
    ...emergencyContact.missingFields.map(field => `emergencyContact.${field}`)
  ];
  
  const totalSections = 3;
  const completedSections = [
    personalDetails.complete,
    medicalInfo.complete,
    emergencyContact.complete
  ].filter(Boolean).length;
  
  const completionPercentage = Math.round((completedSections / totalSections) * 100);
  
  return {
    isComplete: personalDetails.complete && medicalInfo.complete && emergencyContact.complete,
    personalDetailsComplete: personalDetails.complete,
    medicalInfoComplete: medicalInfo.complete,
    emergencyContactComplete: emergencyContact.complete,
    missingFields: allMissingFields,
    completionPercentage
  };
};

/**
 * Update profile completion status in Firestore
 */
export const updateProfileCompletionStatus = async (userId: string, completionResult: ProfileCompletionResult): Promise<void> => {
  try {
    const userRef = doc(db, 'users', userId);
    
    const completionStatus: ProfileCompletionStatus = {
      isComplete: completionResult.isComplete,
      personalDetailsComplete: completionResult.personalDetailsComplete,
      medicalInfoComplete: completionResult.medicalInfoComplete,
      emergencyContactComplete: completionResult.emergencyContactComplete,
      lastUpdated: new Date()
    };
    
    await updateDoc(userRef, {
      profileCompletion: completionStatus,
      updatedAt: new Date()
    });
    
    console.log(`✅ Updated profile completion status for user ${userId}:`, completionStatus);
    
  } catch (error) {
    console.error('❌ Error updating profile completion status:', error);
    throw error;
  }
};

/**
 * Get profile completion status from Firestore
 */
export const getProfileCompletionStatus = async (userId: string): Promise<ProfileCompletionResult | null> => {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      console.log(`❌ User document not found for ID: ${userId}`);
      return null;
    }
    
    const userData = userDoc.data() as UserProfile;
    return checkProfileCompletion(userData);
    
  } catch (error) {
    console.error('❌ Error getting profile completion status:', error);
    return null;
  }
};

/**
 * Check if user needs to complete profile (for first-time login overlay)
 */
export const needsProfileCompletion = async (userId: string): Promise<boolean> => {
  try {
    const completionResult = await getProfileCompletionStatus(userId);
    return completionResult ? !completionResult.isComplete : true;
    
  } catch (error) {
    console.error('❌ Error checking if profile completion needed:', error);
    return true; // Default to requiring completion if error occurs
  }
};

/**
 * Initialize profile completion status for new users
 */
export const initializeProfileCompletion = async (userId: string): Promise<void> => {
  try {
    const userRef = doc(db, 'users', userId);
    
    const initialStatus: ProfileCompletionStatus = {
      isComplete: false,
      personalDetailsComplete: false,
      medicalInfoComplete: false,
      emergencyContactComplete: false,
      lastUpdated: new Date()
    };
    
    await updateDoc(userRef, {
      profileCompletion: initialStatus
    });
    
    console.log(`✅ Initialized profile completion status for user ${userId}`);
    
  } catch (error) {
    console.error('❌ Error initializing profile completion status:', error);
    throw error;
  }
};

/**
 * Get completion progress summary for UI display
 */
export const getCompletionSummary = (completionResult: ProfileCompletionResult): string => {
  const { personalDetailsComplete, medicalInfoComplete, emergencyContactComplete } = completionResult;
  
  const completed = [
    personalDetailsComplete && 'Personal Details',
    medicalInfoComplete && 'Medical Information',
    emergencyContactComplete && 'Emergency Contact'
  ].filter(Boolean);
  
  const remaining = [
    !personalDetailsComplete && 'Personal Details',
    !medicalInfoComplete && 'Medical Information',
    !emergencyContactComplete && 'Emergency Contact'
  ].filter(Boolean);
  
  if (completed.length === 0) {
    return 'Please complete all sections to access the platform.';
  }
  
  if (remaining.length === 0) {
    return 'Profile is complete!';
  }
  
  return `Completed: ${completed.join(', ')}. Remaining: ${remaining.join(', ')}.`;
};
