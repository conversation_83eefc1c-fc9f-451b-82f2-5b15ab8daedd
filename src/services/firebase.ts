import { initializeApp, getApps } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions';

// Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyBBFTnNdPcqA7ujZuEerHR7oacPv6RjfJk",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "university-health-cfe95.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "university-health-cfe95",
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "university-health-cfe95.firebasestorage.app",
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "866843571729",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:866843571729:web:c8984843d1bf3658e723b0"
};

// Initialize Firebase (prevent multiple initialization)
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const functions = getFunctions(app);

// Log successful initialization
console.log('Firebase initialized successfully:', {
  appName: app.name,
  projectId: app.options.projectId
});

// Connect to emulators in development mode
if (import.meta.env.DEV) {
  try {
    // Uncomment these lines when you're running Firebase emulators locally
    // connectAuthEmulator(auth, 'http://localhost:9099');
    // connectFirestoreEmulator(db, 'localhost', 8080);
    // connectStorageEmulator(storage, 'localhost', 9199);
    // connectFunctionsEmulator(functions, 'localhost', 5001);
    
    console.log('Connected to Firebase emulators');
  } catch (error) {
    console.error('Failed to connect to Firebase emulators:', error);
  }
}

export default app;



