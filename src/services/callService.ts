import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  onSnapshot,
  getDoc,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from './firebase';
import type { CallSession, CallService } from '../types/chat';

class FirebaseCallService implements CallService {
  private callsRef = collection(db, 'calls');
  private activeCalls = new Map<string, RTCPeerConnection>();

  // WebRTC configuration
  private rtcConfig: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      { urls: 'stun:stun2.l.google.com:19302' }
    ]
  };

  // Call management
  async initiateCall(conversationId: string, callerId: string, calleeId: string, type: 'video' | 'voice'): Promise<string> {
    try {
      const callData: Omit<CallSession, 'id'> = {
        conversationId,
        callerId,
        calleeId,
        type,
        status: 'initiating',
        iceCandidates: {
          [callerId]: [],
          [calleeId]: []
        }
      };

      const docRef = await addDoc(this.callsRef, {
        ...callData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // Update status to ringing after creation
      await updateDoc(docRef, {
        status: 'ringing',
        updatedAt: serverTimestamp()
      });

      return docRef.id;
    } catch (error) {
      console.error('Error initiating call:', error);
      throw error;
    }
  }

  async acceptCall(callId: string): Promise<void> {
    try {
      const callRef = doc(this.callsRef, callId);
      await updateDoc(callRef, {
        status: 'active',
        startedAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error accepting call:', error);
      throw error;
    }
  }

  async declineCall(callId: string): Promise<void> {
    try {
      const callRef = doc(this.callsRef, callId);
      await updateDoc(callRef, {
        status: 'declined',
        endedAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error declining call:', error);
      throw error;
    }
  }

  async endCall(callId: string): Promise<void> {
    try {
      const callRef = doc(this.callsRef, callId);
      const callDoc = await getDoc(callRef);
      
      if (callDoc.exists()) {
        const callData = callDoc.data() as CallSession;
        const duration = callData.startedAt 
          ? Math.floor((Date.now() - callData.startedAt.toMillis()) / 1000)
          : 0;

        await updateDoc(callRef, {
          status: 'ended',
          endedAt: serverTimestamp(),
          duration,
          updatedAt: serverTimestamp()
        });
      }

      // Clean up WebRTC connection
      const peerConnection = this.activeCalls.get(callId);
      if (peerConnection) {
        peerConnection.close();
        this.activeCalls.delete(callId);
      }
    } catch (error) {
      console.error('Error ending call:', error);
      throw error;
    }
  }

  // WebRTC signaling
  async setOffer(callId: string, offer: RTCSessionDescriptionInit): Promise<void> {
    try {
      const callRef = doc(this.callsRef, callId);
      await updateDoc(callRef, {
        offer,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error setting offer:', error);
      throw error;
    }
  }

  async setAnswer(callId: string, answer: RTCSessionDescriptionInit): Promise<void> {
    try {
      const callRef = doc(this.callsRef, callId);
      await updateDoc(callRef, {
        answer,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error setting answer:', error);
      throw error;
    }
  }

  async addIceCandidate(callId: string, userId: string, candidate: RTCIceCandidateInit): Promise<void> {
    try {
      const callRef = doc(this.callsRef, callId);
      const callDoc = await getDoc(callRef);
      
      if (callDoc.exists()) {
        const callData = callDoc.data() as CallSession;
        const currentCandidates = callData.iceCandidates[userId] || [];
        
        await updateDoc(callRef, {
          [`iceCandidates.${userId}`]: [...currentCandidates, candidate],
          updatedAt: serverTimestamp()
        });
      }
    } catch (error) {
      console.error('Error adding ICE candidate:', error);
      throw error;
    }
  }

  // Call monitoring
  subscribeToCall(callId: string, callback: (call: CallSession) => void): () => void {
    const callRef = doc(this.callsRef, callId);
    
    return onSnapshot(callRef, (doc) => {
      if (doc.exists()) {
        const callData = { id: doc.id, ...doc.data() } as CallSession;
        callback(callData);
      }
    }, (error) => {
      console.error('Error in call subscription:', error);
    });
  }

  subscribeToIncomingCalls(userId: string, callback: (calls: CallSession[]) => void): () => void {
    const q = query(
      this.callsRef,
      where('calleeId', '==', userId),
      where('status', 'in', ['ringing', 'active'])
    );

    return onSnapshot(q, (snapshot) => {
      const calls = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CallSession));
      callback(calls);
    }, (error) => {
      console.error('Error in incoming calls subscription:', error);
    });
  }

  // WebRTC helper methods
  createPeerConnection(callId: string): RTCPeerConnection {
    const peerConnection = new RTCPeerConnection(this.rtcConfig);
    this.activeCalls.set(callId, peerConnection);
    return peerConnection;
  }

  getPeerConnection(callId: string): RTCPeerConnection | undefined {
    return this.activeCalls.get(callId);
  }

  async getUserMedia(video: boolean = true, audio: boolean = true): Promise<MediaStream> {
    try {
      return await navigator.mediaDevices.getUserMedia({
        video: video ? {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        } : false,
        audio: audio ? {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } : false
      });
    } catch (error) {
      console.error('Error getting user media:', error);
      throw error;
    }
  }

  async getDisplayMedia(): Promise<MediaStream> {
    try {
      return await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });
    } catch (error) {
      console.error('Error getting display media:', error);
      throw error;
    }
  }

  // Clean up resources
  cleanup(): void {
    this.activeCalls.forEach((peerConnection) => {
      peerConnection.close();
    });
    this.activeCalls.clear();
  }
}

// Export singleton instance
export const callService = new FirebaseCallService();
export default callService;
