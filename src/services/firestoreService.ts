import type { QueryConstraint } from 'firebase/firestore';
import {
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from './firebase';

// Local type alias to replace Firebase's DocumentData
type DocumentData = Record<string, any>;

/**
 * Generic function to get a document by ID
 */
export const getDocument = async <T>(
  collectionPath: string,
  docId: string
): Promise<T | null> => {
  try {
    const docRef = doc(db, collectionPath, docId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      const data = docSnap.data() as DocumentData;
      
      // Convert Firestore Timestamps to JavaScript Dates
      const processedData = Object.entries(data).reduce((acc, [key, value]) => {
        if (value instanceof Timestamp) {
          acc[key] = value.toDate();
        } else {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);
      
      return { id: docSnap.id, ...processedData } as T;
    }
    
    return null;
  } catch (error) {
    console.error(`Error getting document from ${collectionPath}/${docId}:`, error);
    throw error;
  }
};

/**
 * Generic function to query documents
 */
export const queryDocuments = async <T>(
  collectionPath: string,
  constraints: QueryConstraint[] = [],
  includeIds: boolean = true
): Promise<T[]> => {
  try {
    const collectionRef = collection(db, collectionPath);
    const q = query(collectionRef, ...constraints);
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => {
      const data = doc.data() as DocumentData;
      
      // Convert Firestore Timestamps to JavaScript Dates
      const processedData = Object.entries(data).reduce((acc, [key, value]) => {
        if (value instanceof Timestamp) {
          acc[key] = value.toDate();
        } else {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);
      
      return includeIds ? { id: doc.id, ...processedData } as T : processedData as T;
    });
  } catch (error) {
    console.error(`Error querying documents from ${collectionPath}:`, error);
    throw error;
  }
};

/**
 * Generic function to add a document
 */
export const addDocument = async <T>(
  collectionPath: string,
  data: Omit<T, 'id'>
): Promise<string> => {
  try {
    const collectionRef = collection(db, collectionPath);
    
    // Add timestamps
    const dataWithTimestamps = {
      ...data,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };
    
    const docRef = await addDoc(collectionRef, dataWithTimestamps);
    return docRef.id;
  } catch (error) {
    console.error(`Error adding document to ${collectionPath}:`, error);
    throw error;
  }
};

/**
 * Generic function to update a document
 */
export const updateDocument = async <T>(
  collectionPath: string,
  docId: string,
  data: Partial<T>
): Promise<void> => {
  try {
    const docRef = doc(db, collectionPath, docId);
    
    // Add updated timestamp
    const dataWithTimestamp = {
      ...data,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(docRef, dataWithTimestamp);
  } catch (error) {
    console.error(`Error updating document ${collectionPath}/${docId}:`, error);
    throw error;
  }
};

/**
 * Generic function to delete a document
 */
export const deleteDocument = async (
  collectionPath: string,
  docId: string
): Promise<void> => {
  try {
    const docRef = doc(db, collectionPath, docId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error(`Error deleting document ${collectionPath}/${docId}:`, error);
    throw error;
  }
};

/**
 * Get documents from a subcollection
 */
export const getSubcollection = async <T>(
  parentCollectionPath: string,
  parentDocId: string,
  subcollectionName: string,
  constraints: QueryConstraint[] = []
): Promise<T[]> => {
  try {
    const subcollectionPath = `${parentCollectionPath}/${parentDocId}/${subcollectionName}`;
    return await queryDocuments<T>(subcollectionPath, constraints);
  } catch (error) {
    console.error(`Error getting subcollection ${subcollectionName}:`, error);
    throw error;
  }
};

/**
 * Add document to a subcollection
 */
export const addToSubcollection = async <T>(
  parentCollectionPath: string,
  parentDocId: string,
  subcollectionName: string,
  data: Omit<T, 'id'>
): Promise<string> => {
  try {
    const subcollectionPath = `${parentCollectionPath}/${parentDocId}/${subcollectionName}`;
    return await addDocument<T>(subcollectionPath, data);
  } catch (error) {
    console.error(`Error adding to subcollection ${subcollectionName}:`, error);
    throw error;
  }
};