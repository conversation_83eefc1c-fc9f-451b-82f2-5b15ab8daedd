// Doctor Student Management Service
// Handles doctor-student relationships and student data management

import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { db } from './firebase';
import type { 
  StudentHealthSummary, 
  DoctorStudentAssignment,
  HealthMetric,
  Medication,
  MedicalRecord,
  StudentOverviewStats
} from '../types/medical';
import type { UserProfile } from '../types/firebase';

/**
 * Get all students assigned to a doctor
 */
export const getAssignedStudents = async (doctorId: string): Promise<UserProfile[]> => {
  try {
    // Get doctor assignments
    const assignmentsQuery = query(
      collection(db, 'doctorAssignments'),
      where('doctors', 'array-contains-any', [{ id: doctorId }])
    );
    
    const assignmentsSnapshot = await getDocs(assignmentsQuery);
    const studentIds: string[] = [];
    
    assignmentsSnapshot.forEach(doc => {
      studentIds.push(doc.id); // Document ID is the student ID
    });
    
    if (studentIds.length === 0) {
      return [];
    }
    
    // Get student profiles
    const students: UserProfile[] = [];
    for (const studentId of studentIds) {
      const studentDoc = await getDoc(doc(db, 'users', studentId));
      if (studentDoc.exists()) {
        const studentData = studentDoc.data() as UserProfile;
        students.push({
          ...studentData,
          uid: studentDoc.id
        });
      }
    }
    
    return students;
  } catch (error) {
    console.error('Error getting assigned students:', error);
    throw error;
  }
};

/**
 * Get student health summary for doctor dashboard
 */
export const getStudentHealthSummary = async (studentId: string): Promise<StudentHealthSummary> => {
  try {
    // Get student profile
    const studentDoc = await getDoc(doc(db, 'users', studentId));
    if (!studentDoc.exists()) {
      throw new Error('Student not found');
    }
    
    const studentData = studentDoc.data() as UserProfile;
    
    // Get recent health metrics (last 5)
    const metricsQuery = query(
      collection(db, 'healthMetrics'),
      where('studentId', '==', studentId),
      orderBy('recordedAt', 'desc'),
      limit(5)
    );
    const metricsSnapshot = await getDocs(metricsQuery);
    const recentMetrics: HealthMetric[] = [];
    
    metricsSnapshot.forEach(doc => {
      const data = doc.data();
      recentMetrics.push({
        ...data,
        id: doc.id,
        recordedAt: data.recordedAt.toDate(),
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate()
      } as HealthMetric);
    });
    
    // Count critical alerts
    const criticalMetricsQuery = query(
      collection(db, 'healthMetrics'),
      where('studentId', '==', studentId),
      where('status', '==', 'critical')
    );
    const criticalSnapshot = await getDocs(criticalMetricsQuery);
    const criticalAlerts = criticalSnapshot.size;
    
    // Count active medications
    const activeMedicationsQuery = query(
      collection(db, 'medications'),
      where('studentId', '==', studentId),
      where('isActive', '==', true)
    );
    const medicationsSnapshot = await getDocs(activeMedicationsQuery);
    const activemedications = medicationsSnapshot.size;
    
    // Count upcoming appointments
    const today = new Date();
    const appointmentsQuery = query(
      collection(db, 'appointments'),
      where('studentId', '==', studentId),
      where('status', '==', 'scheduled')
    );
    const appointmentsSnapshot = await getDocs(appointmentsQuery);
    let upcomingAppointments = 0;
    
    appointmentsSnapshot.forEach(doc => {
      const appointmentDate = new Date(doc.data().date);
      if (appointmentDate >= today) {
        upcomingAppointments++;
      }
    });
    
    // Calculate profile completion
    const profileCompletion = calculateProfileCompletion(studentData);
    
    return {
      studentId,
      studentName: `${studentData.firstName || ''} ${studentData.lastName || ''}`.trim() || studentData.displayName || 'Unknown',
      studentEmail: studentData.email,
      lastVisit: studentData.updatedAt ? new Date(studentData.updatedAt) : undefined,
      upcomingAppointments,
      activemedications,
      recentMetrics,
      criticalAlerts,
      profileCompletion,
      lastActivity: studentData.updatedAt ? new Date(studentData.updatedAt) : new Date()
    };
  } catch (error) {
    console.error('Error getting student health summary:', error);
    throw error;
  }
};

/**
 * Get overview statistics for doctor dashboard
 */
export const getDoctorOverviewStats = async (doctorId: string): Promise<StudentOverviewStats> => {
  try {
    const assignedStudents = await getAssignedStudents(doctorId);
    const totalStudents = assignedStudents.length;
    
    // Count active students (those with recent activity)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const activeStudents = assignedStudents.filter(student => {
      const lastActivity = student.updatedAt ? new Date(student.updatedAt) : new Date(0);
      return lastActivity >= thirtyDaysAgo;
    }).length;
    
    // Count students with critical alerts
    let studentsWithCriticalAlerts = 0;
    for (const student of assignedStudents) {
      const criticalQuery = query(
        collection(db, 'healthMetrics'),
        where('studentId', '==', student.uid),
        where('status', '==', 'critical')
      );
      const criticalSnapshot = await getDocs(criticalQuery);
      if (criticalSnapshot.size > 0) {
        studentsWithCriticalAlerts++;
      }
    }
    
    // Calculate average profile completion
    let totalCompletion = 0;
    assignedStudents.forEach(student => {
      totalCompletion += calculateProfileCompletion(student);
    });
    const averageProfileCompletion = totalStudents > 0 ? Math.round(totalCompletion / totalStudents) : 0;
    
    // Get recent activity data (last 7 days)
    const recentActivity = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      date.setHours(0, 0, 0, 0);
      
      const nextDay = new Date(date);
      nextDay.setDate(nextDay.getDate() + 1);
      
      // Count activities for this day (appointments, metrics, etc.)
      let dayCount = 0;
      
      // Count health metrics recorded on this day
      const metricsQuery = query(
        collection(db, 'healthMetrics'),
        where('doctorId', '==', doctorId),
        where('recordedAt', '>=', Timestamp.fromDate(date)),
        where('recordedAt', '<', Timestamp.fromDate(nextDay))
      );
      const metricsSnapshot = await getDocs(metricsQuery);
      dayCount += metricsSnapshot.size;
      
      recentActivity.push({
        date,
        count: dayCount
      });
    }
    
    return {
      totalStudents,
      activeStudents,
      studentsWithCriticalAlerts,
      averageProfileCompletion,
      recentActivity
    };
  } catch (error) {
    console.error('Error getting doctor overview stats:', error);
    throw error;
  }
};

/**
 * Search students by name or email
 */
export const searchAssignedStudents = async (
  doctorId: string, 
  searchTerm: string
): Promise<UserProfile[]> => {
  try {
    const allStudents = await getAssignedStudents(doctorId);
    
    if (!searchTerm.trim()) {
      return allStudents;
    }
    
    const searchLower = searchTerm.toLowerCase();
    
    return allStudents.filter(student => {
      const fullName = `${student.firstName || ''} ${student.lastName || ''}`.toLowerCase();
      const displayName = (student.displayName || '').toLowerCase();
      const email = (student.email || '').toLowerCase();
      const studentId = (student.studentId || '').toLowerCase();
      
      return fullName.includes(searchLower) ||
             displayName.includes(searchLower) ||
             email.includes(searchLower) ||
             studentId.includes(searchLower);
    });
  } catch (error) {
    console.error('Error searching students:', error);
    throw error;
  }
};

/**
 * Get student details for doctor view
 */
export const getStudentDetailsForDoctor = async (
  studentId: string, 
  doctorId: string
): Promise<UserProfile | null> => {
  try {
    // Verify doctor has access to this student
    const assignedStudents = await getAssignedStudents(doctorId);
    const hasAccess = assignedStudents.some(student => student.uid === studentId);
    
    if (!hasAccess) {
      throw new Error('Access denied: Student not assigned to this doctor');
    }
    
    const studentDoc = await getDoc(doc(db, 'users', studentId));
    if (!studentDoc.exists()) {
      return null;
    }
    
    const studentData = studentDoc.data() as UserProfile;
    return {
      ...studentData,
      uid: studentDoc.id
    };
  } catch (error) {
    console.error('Error getting student details:', error);
    throw error;
  }
};

/**
 * Get all students for doctor management UI (wrapper for getAssignedStudents)
 */
export const getDoctorStudents = getAssignedStudents;

/**
 * Calculate profile completion percentage
 */
const calculateProfileCompletion = (profile: UserProfile): number => {
  const requiredFields = [
    'firstName',
    'lastName',
    'dateOfBirth',
    'gender',
    'phoneNumber',
    'address',
    'department',
    'year',
    'major'
  ];
  
  const medicalFields = [
    'medicalInfo.bloodType',
    'medicalInfo.allergies',
    'medicalInfo.medications',
    'medicalInfo.conditions'
  ];
  
  const emergencyFields = [
    'emergencyContact.name',
    'emergencyContact.relationship',
    'emergencyContact.phone'
  ];
  
  let completedFields = 0;
  const totalFields = requiredFields.length + medicalFields.length + emergencyFields.length;
  
  // Check required fields
  requiredFields.forEach(field => {
    if (profile[field as keyof UserProfile]) {
      completedFields++;
    }
  });
  
  // Check medical fields
  if (profile.medicalInfo) {
    if (profile.medicalInfo.bloodType) completedFields++;
    if (Array.isArray(profile.medicalInfo.allergies)) completedFields++;
    if (Array.isArray(profile.medicalInfo.medications)) completedFields++;
    if (Array.isArray(profile.medicalInfo.conditions)) completedFields++;
  }
  
  // Check emergency contact fields
  if (profile.emergencyContact) {
    if (profile.emergencyContact.name) completedFields++;
    if (profile.emergencyContact.relationship) completedFields++;
    if (profile.emergencyContact.phone) completedFields++;
  }
  
  return Math.round((completedFields / totalFields) * 100);
};
