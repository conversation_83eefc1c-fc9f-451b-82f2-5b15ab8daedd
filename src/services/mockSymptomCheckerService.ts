// --- Comprehensive Symptom Database (symptoms.ts) ---

export const symptoms = [
  // General Symptoms (s_1 - s_50)
  { id: 's_1', name: '<PERSON><PERSON>' },
  { id: 's_2', name: 'Fever' },
  { id: 's_3', name: '<PERSON><PERSON>' },
  { id: 's_4', name: '<PERSON><PERSON> Throat' },
  { id: 's_5', name: 'Fatigue' },
  { id: 's_6', name: '<PERSON><PERSON><PERSON>' },
  { id: 's_7', name: 'Vomiting' },
  { id: 's_8', name: 'Diarrhea' },
  { id: 's_9', name: 'Dizziness' },
  { id: 's_10', name: 'Shortness of Breath' },
  { id: 's_11', name: 'Chest Pain' },
  { id: 's_12', name: 'Abdominal Pain' },
  { id: 's_13', name: 'Back Pain' },
  { id: 's_14', name: '<PERSON> Pain' },
  { id: 's_15', name: '<PERSON>s<PERSON> Aches' },
  { id: 's_16', name: '<PERSON><PERSON>' },
  { id: 's_17', name: 'Chills' },
  { id: 's_18', name: 'Sweating' },
  { id: 's_19', name: 'Loss of Appetite' },
  { id: 's_20', name: 'Weight Loss' },
  { id: 's_21', name: 'Insomnia' },
  { id: 's_22', name: 'Anxiety' },
  { id: 's_23', name: 'Depressed Mood' },
  { id: 's_24', name: 'Irritability' },
  { id: 's_25', name: 'Swelling' },
  { id: 's_26', name: 'Weakness' },
  { id: 's_27', name: 'Numbness' },
  { id: 's_28', name: 'Tingling' },
  { id: 's_29', name: 'Bruising' },
  { id: 's_30', name: 'Pale Skin' },
  { id: 's_31', name: 'Dry Mouth' },
  { id: 's_32', name: 'Excessive Thirst' },
  { id: 's_33', name: 'Frequent Urination' },
  { id: 's_34', name: 'Blurred Vision' },
  { id: 's_35', name: 'Earache' },
  { id: 's_36', name: 'Runny Nose' },
  { id: 's_37', name: 'Nasal Congestion' },
  { id: 's_38', name: 'Sneezing' },
  { id: 's_39', name: 'Hoarseness' },
  { id: 's_40', name: 'Difficulty Swallowing' },
  { id: 's_41', name: 'Heart Palpitations' },
  { id: 's_42', name: 'Lightheadedness' },
  { id: 's_43', name: 'Fainting' },
  { id: 's_44', name: 'Tremors' },
  { id: 's_45', name: 'Muscle Spasms' },
  { id: 's_46', name: 'Difficulty Walking' },
  { id: 's_47', name: 'Balance Problems' },
  { id: 's_48', name: 'Memory Loss' },
  { id: 's_49', name: 'Confusion' },
  { id: 's_50', name: 'Slurred Speech' },

  // Respiratory Symptoms (s_51 - s_70)
  { id: 's_51', name: 'Wheezing' },
  { id: 's_52', name: 'Productive Cough' }, // Cough with phlegm
  { id: 's_53', name: 'Dry Cough' },
  { id: 's_54', name: 'Chest Tightness' },
  { id: 's_55', name: 'Rapid Breathing' },
  { id: 's_56', name: 'Shallow Breathing' },
  { id: 's_57', name: 'Coughing Blood' }, // Hemoptysis
  { id: 's_58', name: 'Pain with Breathing' }, // Pleuritic pain
  { id: 's_59', name: 'Bluish Lips/Fingernails' }, // Cyanosis
  { id: 's_60', name: 'Stuffy Nose' },
  { id: 's_61', name: 'Post-nasal Drip' },
  { id: 's_62', name: 'Loss of Smell' },
  { id: 's_63', name: 'Loss of Taste' },
  { id: 's_64', name: 'Throat Irritation' },
  { id: 's_65', name: 'Voice Change' },
  { id: 's_66', name: 'Snoring' },
  { id: 's_67', name: 'Night Sweats' },
  { id: 's_68', name: 'Difficulty Breathing While Lying Down' }, // Orthopnea
  { id: 's_69', name: 'Waking Up Short of Breath' }, // Paroxysmal nocturnal dyspnea
  { id: 's_70', name: 'Recurrent Respiratory Infections' },

  // Cardiovascular Symptoms (s_71 - s_90)
  { id: 's_71', name: 'Angina (Chest Pain with Exertion)' },
  { id: 's_72', name: 'Pressure in Chest' },
  { id: 's_73', name: 'Radiating Arm Pain' }, // Left arm for heart attack
  { id: 's_74', name: 'Jaw Pain (Cardiac)' },
  { id: 's_75', name: 'Swelling in Legs/Ankles' }, // Edema
  { id: 's_76', name: 'Calf Pain with Walking' }, // Claudication
  { id: 's_77', name: 'Cold Extremities' },
  { id: 's_78', name: 'Blue/Purple Discoloration of Skin' },
  { id: 's_79', name: 'High Blood Pressure (symptomatic)' },
  { id: 's_80', name: 'Low Blood Pressure (symptomatic)' },
  { id: 's_81', name: 'Irregular Heartbeat' },
  { id: 's_82', name: 'Fatigue (Cardiac)' },
  { id: 's_83', name: 'Weak Pulse' },
  { id: 's_84', name: 'Rapid Heart Rate' },
  { id: 's_85', name: 'Slow Heart Rate' },
  { id: 's_86', name: 'Shortness of Breath with Exertion' },
  { id: 's_87', name: 'Persistent Cough (Cardiac)' },
  { id: 's_88', name: 'Enlarged Neck Veins' }, // Jugular venous distention
  { id: 's_89', name: 'Abnormal Heart Sounds' },
  { id: 's_90', name: 'Leg Ulcers (Vascular)' },

  // Gastrointestinal Symptoms (s_91 - s_110)
  { id: 's_91', name: 'Heartburn' },
  { id: 's_92', name: 'Acid Reflux' },
  { id: 's_93', name: 'Bloating' },
  { id: 's_94', name: 'Gas' },
  { id: 's_95', name: 'Constipation' },
  { id: 's_96', name: 'Blood in Stool' },
  { id: 's_97', name: 'Black, Tarry Stools' }, // Melena
  { id: 's_98', name: 'Rectal Bleeding' },
  { id: 's_99', name: 'Abdominal Cramps' },
  { id: 's_100', name: 'Loss of Bowel Control' },
  { id: 's_101', name: 'Jaundice (Yellow Skin/Eyes)' },
  { id: 's_102', name: 'Dark Urine' },
  { id: 's_103', name: 'Pale Stools' },
  { id: 's_104', name: 'Difficulty Digesting Food' },
  { id: 's_105', name: 'Increased Abdominal Girth' }, // Ascites
  { id: 's_106', name: 'Pain After Eating' },
  { id: 's_107', name: 'Pain Before Eating' },
  { id: 's_108', name: 'Soreness in Upper Right Abdomen' },
  { id: 's_109', name: 'Soreness in Lower Left Abdomen' },
  { id: 's_110', name: 'Anal Itching' },

  // Neurological Symptoms (s_111 - s_130)
  { id: 's_111', name: 'Severe Headache (Sudden Onset)' },
  { id: 's_112', name: 'Stiff Neck (Neurological)' },
  { id: 's_113', name: 'Sensitivity to Light' }, // Photophobia
  { id: 's_114', name: 'Sensitivity to Sound' }, // Phonophobia
  { id: 's_115', name: 'Aura (Migraine)' },
  { id: 's_116', name: 'Speech Difficulty (Slurred)' },
  { id: 's_117', name: 'Facial Droop' },
  { id: 's_118', name: 'One-Sided Weakness' },
  { id: 's_119', name: 'Vision Changes (Sudden)' },
  { id: 's_120', name: 'Loss of Consciousness' },
  { id: 's_121', name: 'Seizures' },
  { id: 's_122', name: 'Difficulty Concentrating' },
  { id: 's_123', name: 'Short-Term Memory Loss' },
  { id: 's_124', name: 'Disorientation' },
  { id: 's_125', name: 'Difficulty with Coordination' },
  { id: 's_126', name: 'Gait Disturbance' },
  { id: 's_127', name: 'Involuntary Movements' },
  { id: 's_128', name: 'Loss of Sensation' },
  { id: 's_129', name: 'Vertigo' },
  { id: 's_130', name: 'Ringing in Ears' }, // Tinnitus

  // Musculoskeletal Symptoms (s_131 - s_150)
  { id: 's_131', name: 'Joint Stiffness' },
  { id: 's_132', name: 'Swollen Joints' },
  { id: 's_133', name: 'Redness Around Joints' },
  { id: 's_134', name: 'Warmth Around Joints' },
  { id: 's_135', name: 'Limited Range of Motion' },
  { id: 's_136', name: 'Muscle Weakness' },
  { id: 's_137', name: 'Muscle Cramps' },
  { id: 's_138', name: 'Bone Pain' },
  { id: 's_139', name: 'Fracture (Pain/Deformity)' },
  { id: 's_140', name: 'Lower Back Pain (Chronic)' },
  { id: 's_141', name: 'Neck Pain (Chronic)' },
  { id: 's_142', name: 'Shoulder Pain' },
  { id: 's_143', name: 'Knee Pain' },
  { id: 's_144', name: 'Hip Pain' },
  { id: 's_145', name: 'Hand/Wrist Pain' },
  { id: 's_146', name: 'Foot/Ankle Pain' },
  { id: 's_147', name: 'Pain with Movement' },
  { id: 's_148', name: 'Clicking/Popping in Joints' },
  { id: 's_149', name: 'Loss of Muscle Mass' },
  { id: 's_150', name: 'Difficulty Standing/Sitting' },

  // Dermatological Symptoms (s_151 - s_170)
  { id: 's_151', name: 'Itching (Generalized)' },
  { id: 's_152', name: 'Hives' },
  { id: 's_153', name: 'Blisters' },
  { id: 's_154', name: 'Dry Skin' },
  { id: 's_155', name: 'Red Patches on Skin' },
  { id: 's_156', name: 'Flaking Skin' },
  { id: 's_157', name: 'Skin Lesions' },
  { id: 's_158', name: 'Changes in Moles' },
  { id: 's_159', name: 'Skin Discoloration' },
  { id: 's_160', name: 'Hair Loss' },
  { id: 's_161', name: 'Brittle Nails' },
  { id: 's_162', name: 'Nail Discoloration' },
  { id: 's_163', name: 'Excessive Sweating (Hyperhidrosis)' },
  { id: 's_164', name: 'Acne' },
  { id: 's_165', name: 'Pimples' },
  { id: 's_166', name: 'Skin Ulcers' },
  { id: 's_167', name: 'Warts' },
  { id: 's_168', name: 'Birthmark Changes' },
  { id: 's_169', name: 'Stretch Marks (New/Unexplained)' },
  { id: 's_170', name: 'Spider Veins' },

  // Eye Symptoms (s_171 - s_180)
  { id: 's_171', name: 'Eye Redness' },
  { id: 's_172', name: 'Itchy Eyes' },
  { id: 's_173', name: 'Watery Eyes' },
  { id: 's_174', name: 'Eye Discharge' },
  { id: 's_175', name: 'Eye Pain' },
  { id: 's_176', name: 'Double Vision' },
  { id: 's_177', name: 'Floaters in Vision' },
  { id: 's_178', name: 'Light Sensitivity (Eyes)' },
  { id: 's_179', name: 'Dry Eyes' },
  { id: 's_180', name: 'Vision Loss (Sudden)' },

  // Ear, Nose, Throat (ENT) Symptoms (s_181 - s_190)
  { id: 's_181', name: 'Ear Pain (Severe)' },
  { id: 's_182', name: 'Ringing in Ears (Persistent)' },
  { id: 's_183', name: 'Hearing Loss (Sudden)' },
  { id: 's_184', name: 'Nosebleed (Frequent/Severe)' },
  { id: 's_185', name: 'Difficulty Breathing Through Nose' },
  { id: 's_186', name: 'Throat Pain (Severe)' },
  { id: 's_187', name: 'Swollen Glands in Neck' },
  { id: 's_188', name: 'Bad Breath (Persistent)' },
  { id: 's_189', name: 'Mouth Sores' },
  { id: 's_190', name: 'Difficulty Opening Mouth' },

  // Urinary Symptoms (s_191 - s_200)
  { id: 's_191', name: 'Painful Urination' }, // Dysuria
  { id: 's_192', name: 'Increased Urgency to Urinate' },
  { id: 's_193', name: 'Difficulty Starting Urination' },
  { id: 's_194', name: 'Blood in Urine' }, // Hematuria
  { id: 's_195', name: 'Cloudy Urine' },
  { id: 's_196', name: 'Strong-Smelling Urine' },
  { id: 's_197', name: 'Loss of Bladder Control' },
  { id: 's_198', name: 'Flank Pain' }, // Kidney area
  { id: 's_199', name: 'Decreased Urine Output' },
  { id: 's_200', name: 'Burning Sensation During Urination' },

  // Psychological/Mental Health Symptoms (s_201 - s_210)
  { id: 's_201', name: 'Panic Attacks' },
  { id: 's_202', name: 'Persistent Sadness' },
  { id: 's_203', name: 'Loss of Interest/Pleasure' },
  { id: 's_204', name: 'Changes in Sleep Pattern' },
  { id: 's_205', name: 'Changes in Appetite' },
  { id: 's_206', name: 'Feelings of Worthlessness' },
  { id: 's_207', name: 'Difficulty Making Decisions' },
  { id: 's_208', name: 'Suicidal Thoughts' },
  { id: 's_209', name: 'Hallucinations' },
  { id: 's_210', name: 'Delusions' },

  // Miscellaneous/Systemic Symptoms (s_211 - s_220)
  { id: 's_211', name: 'Unexplained Weight Gain' },
  { id: 's_212', name: 'Unexplained Weakness' },
  { id: 's_213', name: 'Increased Sensitivity to Cold' },
  { id: 's_214', name: 'Increased Sensitivity to Heat' },
  { id: 's_215', name: 'Goiter (Swollen Thyroid)' },
  { id: 's_216', name: 'Enlarged Lymph Nodes' },
  { id: 's_217', name: 'Easy Bleeding' },
  { id: 's_218', name: 'Chronic Wounds' },
  { id: 's_219', name: 'Unexplained Bruising' },
  { id: 's_220', name: 'General Malaise' },
];

// --- Comprehensive Conditions Database (conditions.ts) ---

// Add this helper function at the top of your file
// This will be used to safely calculate specificity without division by zero
const calculateSpecificity = (matchCount: number, selectedSymptoms: string[]) => {
  if (!selectedSymptoms || selectedSymptoms.length === 0) {
    return 0;
  }
  const specificity = matchCount / selectedSymptoms.length;
  // Ensure we return a valid number between 0 and 1
  return isNaN(specificity) ? 0 : Math.min(1, Math.max(0, specificity));
};

// Helper function to ensure probability calculations are safe
const safeProbabilityCalculation = (result: number): number => {
  if (isNaN(result) || !isFinite(result)) {
    return 0;
  }
  return Math.min(1, Math.max(0, result));
};

export const conditions = [
  {
    id: 'c_001',
    name: 'Common Cold',
    symptoms: ['s_2', 's_3', 's_4', 's_36', 's_37', 's_5', 's_60'], // Added s_60 (Stuffy Nose)
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      // Ensure we don't divide by zero
      const coverage = matchCount / 7; // 7 is the total number of symptoms for this condition (added s_60)
      const specificity = calculateSpecificity(matchCount, selectedSymptoms);
      // Accept either s_37 (Nasal Congestion) or s_60 (Stuffy Nose) as stuffy nose symptoms
      const hasRunnyNose = selectedSymptoms.includes('s_36');
      const hasStuffyNose = selectedSymptoms.includes('s_37') || selectedSymptoms.includes('s_60');
      const hasKeySymptoms = hasRunnyNose && hasStuffyNose; // Runny + any stuffy nose
      const keySymptomBoost = hasKeySymptoms ? 0.2 : 0;

      // Ensure we return a valid number
      const result = Math.min(0.90, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
      return isNaN(result) ? 0 : Math.max(0, result);
    },
    triage_level: 'self_care',
    description: 'A viral infection of your nose and throat (upper respiratory tract).',
    treatment: 'Rest, fluids, over-the-counter pain relievers, decongestants.',
    risk_factors: ['Age (young children)', 'Weakened immune system', 'Smoking', 'Exposure to sick people']
  },
  {
    id: 'c_002',
    name: 'Influenza (Flu)',
    symptoms: ['s_2', 's_3', 's_5', 's_15', 's_1', 's_17', 's_19', 's_10'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 8;
      const specificity = calculateSpecificity(matchCount, selectedSymptoms);
      const hasKeySymptoms = selectedSymptoms.includes('s_2') && selectedSymptoms.includes('s_5') && selectedSymptoms.includes('s_15'); // Fever, Fatigue, Muscle Aches
      const keySymptomBoost = hasKeySymptoms ? 0.3 : 0;
      let ageAdjustment = 1.0;
      if (userData && userData.age) {
        if (userData.age > 65 || userData.age < 5) ageAdjustment = 1.3;
      }
      const result = Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * ageAdjustment);
      return isNaN(result) ? 0 : Math.max(0, result);
    },
    triage_level: 'consultation',
    description: 'A contagious respiratory illness caused by influenza viruses that infect the nose, throat, and sometimes the lungs.',
    treatment: 'Rest, fluids, antiviral medications (if prescribed), pain relievers.',
    risk_factors: ['Age (very young or elderly)', 'Chronic medical conditions', 'Weakened immune system', 'Pregnancy']
  },
  {
    id: 'c_003',
    name: 'Streptococcal Pharyngitis (Strep Throat)',
    symptoms: ['s_4', 's_2', 's_40', 's_1', 's_19', 's_187'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = calculateSpecificity(matchCount, selectedSymptoms);
      const hasKeySymptoms = selectedSymptoms.includes('s_4') && selectedSymptoms.includes('s_40'); // Sore Throat, Difficulty Swallowing
      const keySymptomBoost = hasKeySymptoms ? 0.4 : 0;
      const result = Math.min(0.98, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
      return isNaN(result) ? 0 : Math.max(0, result);
    },
    triage_level: 'consultation',
    description: 'A bacterial infection that can make your throat feel sore and scratchy.',
    treatment: 'Antibiotics.',
    risk_factors: ['Age (children 5-15)', 'Close contact with an infected person']
  },
  {
    id: 'c_004',
    name: 'Pneumonia',
    symptoms: ['s_2', 's_3', 's_10', 's_5', 's_52', 's_11', 's_58'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasFever = selectedSymptoms.includes('s_2');
      const hasCough = selectedSymptoms.includes('s_3') || selectedSymptoms.includes('s_52');
      const hasSOB = selectedSymptoms.includes('s_10');
      const keySymptomBoost = (hasFever && hasCough && hasSOB) ? 0.3 : 0;

      let ageAdjustment = 1.0;
      if (userData && userData.age) {
        if (userData.age > 65 || userData.age < 5) ageAdjustment = 1.4;
      }
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * ageAdjustment);
    },
    triage_level: 'consultation',
    description: 'An infection that inflames the air sacs in one or both lungs, which may fill with fluid.',
    treatment: 'Antibiotics for bacterial pneumonia, antiviral medications for viral pneumonia, rest, fluids, oxygen therapy if needed.',
    risk_factors: ['Age (very young or elderly)', 'Smoking', 'Chronic diseases', 'Weakened immune system', 'Hospitalization']
  },
  {
    id: 'c_005',
    name: 'Bronchitis',
    symptoms: ['s_3', 's_52', 's_54', 's_5', 's_17', 's_4'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasPersistentCough = selectedSymptoms.includes('s_3') && selectedSymptoms.includes('s_52');
      const keySymptomBoost = hasPersistentCough ? 0.2 : 0;
      return Math.min(0.85, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'self_care',
    description: 'Inflammation of the lining of your bronchial tubes, which carry air to and from your lungs.',
    treatment: 'Rest, fluids, cough suppressants, bronchodilators if wheezing is present.',
    risk_factors: ['Smoking', 'Exposure to irritants (dust, chemicals)', 'Weakened immune system']
  },
  {
    id: 'c_006',
    name: 'Asthma',
    symptoms: ['s_10', 's_51', 's_54', 's_3', 's_86', 's_68'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasWheezingSOB = selectedSymptoms.includes('s_51') && selectedSymptoms.includes('s_10');
      const keySymptomBoost = hasWheezingSOB ? 0.4 : 0;
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'A condition in which your airways narrow and swell and may produce extra mucus.',
    treatment: 'Inhalers (bronchodilators, corticosteroids), nebulizers, avoiding triggers.',
    risk_factors: ['Family history of asthma', 'Allergies', 'Exposure to irritants/allergens', 'Respiratory infections']
  },
  {
    id: 'c_007',
    name: 'Allergic Rhinitis (Hay Fever)',
    symptoms: ['s_36', 's_37', 's_38', 's_172', 's_173', 's_1'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasNasalSymptoms = selectedSymptoms.includes('s_36') && selectedSymptoms.includes('s_37') && selectedSymptoms.includes('s_38');
      const keySymptomBoost = hasNasalSymptoms ? 0.3 : 0;
      return Math.min(0.85, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'self_care',
    description: 'An allergic reaction that causes sneezing, stuffy nose, watery eyes and other symptoms.',
    treatment: 'Antihistamines, nasal corticosteroids, decongestants, avoiding allergens.',
    risk_factors: ['Family history of allergies/asthma', 'Exposure to pollen, dust mites, pet dander']
  },
  {
    id: 'c_008',
    name: 'Urinary Tract Infection (UTI)',
    symptoms: ['s_191', 's_192', 's_33', 's_195', 's_12', 's_198'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasKeySymptoms = selectedSymptoms.includes('s_191') && selectedSymptoms.includes('s_192'); // Painful urination, urgency
      const keySymptomBoost = hasKeySymptoms ? 0.4 : 0;
      let sexAdjustment = 1.0;
      if (userData && userData.sex && userData.sex.toLowerCase() === 'female') sexAdjustment = 1.5;
      return Math.min(0.98, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * sexAdjustment);
    },
    triage_level: 'consultation',
    description: 'An infection in any part of your urinary system — your kidneys, ureters, bladder and urethra.',
    treatment: 'Antibiotics.',
    risk_factors: ['Female sex', 'Sexual activity', 'Diabetes', 'Kidney stones', 'Enlarged prostate (men)']
  },
  {
    id: 'c_009',
    name: 'Gastroenteritis (Stomach Flu)',
    symptoms: ['s_8', 's_7', 's_6', 's_12', 's_2', 's_19'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasKeySymptoms = selectedSymptoms.includes('s_8') && selectedSymptoms.includes('s_7'); // Diarrhea, Vomiting
      const keySymptomBoost = hasKeySymptoms ? 0.3 : 0;
      return Math.min(0.90, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'self_care',
    description: 'An inflammation of the stomach and intestines, typically caused by viral or bacterial infection.',
    treatment: 'Rest, fluids, electrolyte replacement, bland diet.',
    risk_factors: ['Exposure to contaminated food/water', 'Close contact with infected individuals']
  },
  {
    id: 'c_010',
    name: 'Migraine',
    symptoms: ['s_1', 's_113', 's_114', 's_6', 's_7', 's_115', 's_9'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasKeySymptoms = selectedSymptoms.includes('s_1') && (selectedSymptoms.includes('s_113') || selectedSymptoms.includes('s_114'));
      const keySymptomBoost = hasKeySymptoms ? 0.4 : 0;
      let sexAdjustment = 1.0;
      if (userData && userData.sex && userData.sex.toLowerCase() === 'female') sexAdjustment = 1.3;
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * sexAdjustment);
    },
    triage_level: 'consultation',
    description: 'A type of headache that can cause severe throbbing pain or a pulsing sensation, usually on one side of the head.',
    treatment: 'Pain relievers (NSAIDs, triptans), anti-nausea medication, rest in a dark, quiet room.',
    risk_factors: ['Family history', 'Female sex', 'Stress', 'Hormonal changes', 'Certain foods/drinks']
  },
  {
    id: 'c_011',
    name: 'Tension Headache',
    symptoms: ['s_1', 's_13', 's_26', 's_112'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 4;
      const specificity = matchCount / selectedSymptoms.length;
      const hasHeadacheAndMuscleAches = selectedSymptoms.includes('s_1') && selectedSymptoms.includes('s_13');
      const keySymptomBoost = hasHeadacheAndMuscleAches ? 0.2 : 0;
      return Math.min(0.80, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'self_care',
    description: 'A common type of headache that causes mild to moderate pain, often described as a tight band around the head.',
    treatment: 'Over-the-counter pain relievers, stress management, relaxation techniques.',
    risk_factors: ['Stress', 'Lack of sleep', 'Poor posture', 'Eye strain']
  },
  {
    id: 'c_012',
    name: 'Appendicitis',
    symptoms: ['s_12', 's_6', 's_7', 's_2', 's_19', 's_109'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasRLQPain = selectedSymptoms.includes('s_12') && selectedSymptoms.includes('s_109'); // Abdominal Pain, Lower Left Abdominal Pain (should be RLQ, adjust s_109 or add new symptom)
      // Note: s_109 is "Soreness in Lower Left Abdomen", appendicitis is typically lower right. This needs careful consideration.
      // For this example, assuming s_109 can broadly represent lower quadrant pain or a new symptom for RLQ pain is needed.
      const keySymptomBoost = hasRLQPain ? 0.5 : 0;
      return Math.min(0.99, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'emergency',
    description: 'An inflammation of the appendix, a finger-shaped pouch that projects from your colon.',
    treatment: 'Appendectomy (surgical removal of the appendix).',
    risk_factors: ['Age (10-30 years)', 'Family history', 'Diet low in fiber']
  },
  {
    id: 'c_013',
    name: 'Kidney Stones',
    symptoms: ['s_198', 's_191', 's_7', 's_6', 's_194', 's_12'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasKeySymptoms = selectedSymptoms.includes('s_198') && selectedSymptoms.includes('s_191'); // Flank pain, painful urination
      const keySymptomBoost = hasKeySymptoms ? 0.4 : 0;
      return Math.min(0.98, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'emergency',
    description: 'Hard deposits of minerals and salt that form inside your kidneys.',
    treatment: 'Pain relief, increased fluid intake, medications to help pass stones, lithotripsy, surgery.',
    risk_factors: ['Dehydration', 'Family history', 'Diet', 'Obesity', 'Digestive diseases', 'UTIs']
  },
  {
    id: 'c_014',
    name: 'Gastroesophageal Reflux Disease (GERD)',
    symptoms: ['s_91', 's_92', 's_40', 's_53', 's_39', 's_104'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasKeySymptoms = selectedSymptoms.includes('s_91') || selectedSymptoms.includes('s_92'); // Heartburn or Acid Reflux
      const keySymptomBoost = hasKeySymptoms ? 0.3 : 0;
      return Math.min(0.90, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'A chronic digestive disease in which stomach acid or, occasionally, stomach content, flows back into your food pipe (esophagus).',
    treatment: 'Antacids, H2 blockers, proton pump inhibitors, lifestyle changes (diet, weight loss).',
    risk_factors: ['Obesity', 'Hiatal hernia', 'Pregnancy', 'Smoking', 'Certain foods/drinks']
  },
  {
    id: 'c_015',
    name: 'Depression',
    symptoms: ['s_23', 's_202', 's_203', 's_204', 's_205', 's_5', 's_208'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasCoreSymptoms = selectedSymptoms.includes('s_23') && (selectedSymptoms.includes('s_202') || selectedSymptoms.includes('s_203'));
      const keySymptomBoost = hasCoreSymptoms ? 0.4 : 0;
      // If suicidal thoughts, highly suggest consultation
      const emergencyBoost = selectedSymptoms.includes('s_208') ? 0.5 : 0;
      return Math.min(0.98, ((coverage * 0.7 + keySymptomBoost + emergencyBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'A mood disorder that causes a persistent feeling of sadness and loss of interest.',
    treatment: 'Psychotherapy, antidepressant medications, lifestyle changes (exercise, diet, sleep).',
    risk_factors: ['Family history', 'Trauma/Stress', 'Other mental health disorders', 'Chronic illness']
  },
  {
    id: 'c_016',
    name: 'Anxiety Disorder',
    symptoms: ['s_22', 's_41', 's_9', 's_10', 's_201', 's_24', 's_21'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasKeySymptoms = selectedSymptoms.includes('s_22') && (selectedSymptoms.includes('s_41') || selectedSymptoms.includes('s_10'));
      const keySymptomBoost = hasKeySymptoms ? 0.3 : 0;
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'A mental health disorder characterized by feelings of worry, anxiety, or fear that are strong enough to interfere with one\'s daily activities.',
    treatment: 'Psychotherapy, medications (anti-anxiety drugs, antidepressants), stress management techniques.',
    risk_factors: ['Family history', 'Stressful life events', 'Certain personality traits', 'Other mental health disorders']
  },
  {
    id: 'c_017',
    name: 'Type 2 Diabetes Mellitus',
    symptoms: ['s_32', 's_33', 's_20', 's_5', 's_34', 's_211', 's_218'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasKeySymptoms = selectedSymptoms.includes('s_32') && selectedSymptoms.includes('s_33'); // Polydipsia and Polyuria
      const keySymptomBoost = hasKeySymptoms ? 0.4 : 0;
      let ageAdjustment = 1.0;
      if (userData && userData.age && userData.age >= 45) ageAdjustment = 1.3;
      return Math.min(0.98, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * ageAdjustment);
    },
    triage_level: 'consultation',
    description: 'A chronic condition that affects the way your body processes blood sugar (glucose).',
    treatment: 'Dietary changes, exercise, oral medications, insulin injections.',
    risk_factors: ['Obesity', 'Family history', 'Physical inactivity', 'Age (45+)', 'Certain ethnicities']
  },
  {
    id: 'c_018',
    name: 'Hypertension (High Blood Pressure)',
    symptoms: ['s_1', 's_42', 's_34', 's_184', 's_79'], // Note: s_79 represents symptomatic high BP, often hypertension is asymptomatic.
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 5;
      const specificity = matchCount / selectedSymptoms.length;
      // Hypertension is often asymptomatic, so direct symptom matching is less predictive than risk factors/measurements.
      // This probability function is simplified for a symptom checker context.
      const hasSevereHeadache = selectedSymptoms.includes('s_111'); // Indicates hypertensive crisis for symptom checker
      const keySymptomBoost = hasSevereHeadache ? 0.5 : 0;
      let ageAdjustment = 1.0;
      if (userData && userData.age && userData.age >= 40) ageAdjustment = 1.2;
      return Math.min(0.90, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * ageAdjustment);
    },
    triage_level: 'consultation',
    description: 'A common condition in which the long-term force of the blood against your artery walls is high enough that it may eventually cause health problems, such as heart disease.',
    treatment: 'Lifestyle changes (diet, exercise, weight loss), medications (diuretics, ACE inhibitors, ARBs, beta-blockers).',
    risk_factors: ['Age', 'Family history', 'Obesity', 'Physical inactivity', 'Smoking', 'High sodium diet', 'Stress']
  },
  {
    id: 'c_019',
    name: 'Coronary Artery Disease (CAD)',
    symptoms: ['s_11', 's_71', 's_72', 's_73', 's_10', 's_86', 's_5'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasAngina = selectedSymptoms.includes('s_11') || selectedSymptoms.includes('s_71') || selectedSymptoms.includes('s_72');
      const keySymptomBoost = hasAngina ? 0.4 : 0;
      let ageAdjustment = 1.0;
      if (userData && userData.age) {
        if (userData.age > 50) ageAdjustment = 1.3;
      }
      return Math.min(0.98, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * ageAdjustment);
    },
    triage_level: 'consultation',
    description: 'A condition in which the major blood vessels that supply the heart with blood, oxygen and nutrients (coronary arteries) become damaged or diseased.',
    treatment: 'Lifestyle changes, medications (statins, aspirin, beta-blockers), angioplasty, bypass surgery.',
    risk_factors: ['High blood pressure', 'High cholesterol', 'Diabetes', 'Smoking', 'Obesity', 'Family history', 'Age']
  },
  {
    id: 'c_020',
    name: 'Myocardial Infarction (Heart Attack)',
    symptoms: ['s_11', 's_72', 's_73', 's_74', 's_10', 's_6', 's_18', 's_43'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 8;
      const specificity = matchCount / selectedSymptoms.length;
      const hasCriticalSymptoms = (selectedSymptoms.includes('s_11') || selectedSymptoms.includes('s_72')) && (selectedSymptoms.includes('s_73') || selectedSymptoms.includes('s_74'));
      const keySymptomBoost = hasCriticalSymptoms ? 0.6 : 0;
      let ageAdjustment = 1.0;
      if (userData && userData.age && userData.age > 45) ageAdjustment = 1.5;
      return Math.min(0.99, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * ageAdjustment);
    },
    triage_level: 'emergency',
    description: 'Occurs when blood flow to the heart is blocked. The blockage is most often a buildup of fat, cholesterol and other substances, which form a plaque in the arteries that feed the heart (coronary arteries).',
    treatment: 'Emergency medical intervention (aspirin, nitroglycerin, oxygen), angioplasty, stent placement, bypass surgery.',
    risk_factors: ['High blood pressure', 'High cholesterol', 'Diabetes', 'Smoking', 'Obesity', 'Family history', 'Age']
  },
  {
    id: 'c_021',
    name: 'Stroke',
    symptoms: ['s_116', 's_117', 's_118', 's_119', 's_1', 's_120', 's_49', 's_125'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 8;
      const specificity = matchCount / selectedSymptoms.length;
      const hasFASTSymptoms = (selectedSymptoms.includes('s_117') || selectedSymptoms.includes('s_118') || selectedSymptoms.includes('s_116'));
      const keySymptomBoost = hasFASTSymptoms ? 0.7 : 0;
      let ageAdjustment = 1.0;
      if (userData && userData.age && userData.age > 55) ageAdjustment = 1.5;
      return Math.min(0.99, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * ageAdjustment);
    },
    triage_level: 'emergency',
    description: 'Occurs when the blood supply to part of your brain is interrupted or reduced, depriving brain tissue of oxygen and nutrients.',
    treatment: 'Emergency medical treatment (clot-busting drugs, clot removal), rehabilitation (physical, occupational, speech therapy).',
    risk_factors: ['High blood pressure', 'High cholesterol', 'Diabetes', 'Smoking', 'Obesity', 'Heart disease', 'Age', 'Family history']
  },
  {
    id: 'c_022',
    name: 'Irritable Bowel Syndrome (IBS)',
    symptoms: ['s_12', 's_8', 's_95', 's_93', 's_99', 's_100'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasCoreSymptoms = selectedSymptoms.includes('s_12') && (selectedSymptoms.includes('s_8') || selectedSymptoms.includes('s_95'));
      const keySymptomBoost = hasCoreSymptoms ? 0.3 : 0;
      let sexAdjustment = 1.0;
      if (userData && userData.sex && userData.sex.toLowerCase() === 'female') sexAdjustment = 1.2;
      return Math.min(0.90, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * sexAdjustment);
    },
    triage_level: 'consultation',
    description: 'A common disorder that affects the large intestine. Signs and symptoms include cramping, abdominal pain, bloating, gas, and diarrhea or constipation, or both.',
    treatment: 'Dietary changes, stress management, medications to manage symptoms (laxatives, anti-diarrheals).',
    risk_factors: ['Female sex', 'Age (under 50)', 'Family history', 'Stressful life events', 'Infection']
  },
  {
    id: 'c_023',
    name: 'Gout',
    symptoms: ['s_14', 's_132', 's_133', 's_134', 's_135', 's_146'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasAcuteJointPain = selectedSymptoms.includes('s_14') && selectedSymptoms.includes('s_132') && selectedSymptoms.includes('s_133');
      const keySymptomBoost = hasAcuteJointPain ? 0.4 : 0;
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'A form of arthritis characterized by severe pain, redness, tenderness, and swelling in joints, often the big toe.',
    treatment: 'NSAIDs, colchicine, corticosteroids, allopurinol (for prevention), dietary changes.',
    risk_factors: ['High purine diet', 'Obesity', 'Alcohol consumption', 'Certain medications (diuretics)', 'Kidney disease']
  },
  {
    id: 'c_024',
    name: 'Rheumatoid Arthritis',
    symptoms: ['s_14', 's_131', 's_132', 's_5', 's_19', 's_20', 's_145'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasMorningStiffness = selectedSymptoms.includes('s_131');
      const keySymptomBoost = hasMorningStiffness ? 0.3 : 0;
      let sexAdjustment = 1.0;
      if (userData && userData.sex && userData.sex.toLowerCase() === 'female') sexAdjustment = 1.3;
      return Math.min(0.98, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * sexAdjustment);
    },
    triage_level: 'consultation',
    description: 'A chronic inflammatory disorder that can affect more than just your joints. It can cause bone erosion and joint deformity.',
    treatment: 'DMARDs (Disease-Modifying Antirheumatic Drugs), biologics, NSAIDs, corticosteroids, physical therapy.',
    risk_factors: ['Female sex', 'Age (40-60)', 'Family history', 'Smoking', 'Obesity']
  },
  {
    id: 'c_025',
    name: 'Osteoarthritis',
    symptoms: ['s_14', 's_131', 's_135', 's_147', 's_148', 's_143'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasPainWithMovement = selectedSymptoms.includes('s_147');
      const keySymptomBoost = hasPainWithMovement ? 0.2 : 0;
      let ageAdjustment = 1.0;
      if (userData && userData.age && userData.age > 50) ageAdjustment = 1.4;
      return Math.min(0.90, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * ageAdjustment);
    },
    triage_level: 'consultation',
    description: 'The most common form of arthritis, affecting millions of people worldwide. It occurs when the protective cartilage that cushions the ends of your bones wears down over time.',
    treatment: 'Pain relievers, physical therapy, exercise, weight management, joint replacement surgery.',
    risk_factors: ['Age', 'Obesity', 'Joint injury', 'Repetitive stress on a joint', 'Genetics', 'Bone deformities']
  },
  {
    id: 'c_026',
    name: 'Fibromyalgia',
    symptoms: ['s_5', 's_15', 's_14', 's_21', 's_22', 's_122', 's_137'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasWidespreadPain = selectedSymptoms.includes('s_15') && selectedSymptoms.includes('s_14');
      const keySymptomBoost = hasWidespreadPain ? 0.3 : 0;
      let sexAdjustment = 1.0;
      if (userData && userData.sex && userData.sex.toLowerCase() === 'female') sexAdjustment = 1.5;
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * sexAdjustment);
    },
    triage_level: 'consultation',
    description: 'A chronic disorder characterized by widespread musculoskeletal pain accompanied by fatigue, sleep, memory and mood issues.',
    treatment: 'Pain relievers, antidepressants, anti-seizure drugs, exercise, stress reduction, therapy.',
    risk_factors: ['Female sex', 'Family history', 'Other disorders (RA, lupus)', 'Physical or emotional trauma']
  },
  {
    id: 'c_027',
    name: 'Sciatica',
    symptoms: ['s_13', 's_27', 's_28', 's_46', 's_140', 's_15'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasRadiatingLegPain = selectedSymptoms.includes('s_13') && (selectedSymptoms.includes('s_27') || selectedSymptoms.includes('s_28'));
      const keySymptomBoost = hasRadiatingLegPain ? 0.4 : 0;
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'Pain that radiates along the path of the sciatic nerve, which branches from your lower back through your hips and buttocks and down each leg.',
    treatment: 'Pain relievers, physical therapy, exercise, corticosteroids, surgery (rarely).',
    risk_factors: ['Age', 'Obesity', 'Occupation requiring heavy lifting', 'Prolonged sitting', 'Diabetes']
  },
  {
    id: 'c_028',
    name: 'Osteoporosis',
    symptoms: ['s_138', 's_139', 's_20', 's_13', 's_26'], // Often asymptomatic until fracture
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 5;
      const specificity = matchCount / selectedSymptoms.length;
      // Osteoporosis is often asymptomatic until a fracture, so symptom matching is less direct.
      // This function reflects symptoms associated with complications like fractures.
      const hasFracture = selectedSymptoms.includes('s_139');
      const keySymptomBoost = hasFracture ? 0.5 : 0;
      let ageAdjustment = 1.0;
      if (userData && userData.age && userData.age >= 50) ageAdjustment = 1.5; // Especially post-menopausal women
      let sexAdjustment = 1.0;
      if (userData && userData.sex && userData.sex.toLowerCase() === 'female') sexAdjustment = 1.3;
      return Math.min(0.90, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * ageAdjustment * sexAdjustment);
    },
    triage_level: 'consultation',
    description: 'A condition in which bones become weak and brittle — so brittle that a fall or even mild stresses such as coughing or bending over can cause a fracture.',
    treatment: 'Medications (bisphosphonates), calcium and vitamin D supplements, weight-bearing exercise, fall prevention.',
    risk_factors: ['Female sex', 'Age', 'Family history', 'Low calcium intake', 'Sedentary lifestyle', 'Smoking', 'Certain medications']
  },
  {
    id: 'c_029',
    name: 'Anemia',
    symptoms: ['s_5', 's_26', 's_10', 's_42', 's_30', 's_83', 's_19'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasCoreSymptoms = selectedSymptoms.includes('s_5') && selectedSymptoms.includes('s_26');
      const keySymptomBoost = hasCoreSymptoms ? 0.3 : 0;
      let sexAdjustment = 1.0;
      if (userData && userData.sex && userData.sex.toLowerCase() === 'female') sexAdjustment = 1.2; // Due to menstrual bleeding
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * sexAdjustment);
    },
    triage_level: 'consultation',
    description: 'A condition in which you lack enough healthy red blood cells to carry adequate oxygen to your body\'s tissues.',
    treatment: 'Iron supplements, vitamin B12 supplements, dietary changes, addressing underlying cause.',
    risk_factors: ['Diet low in iron/vitamins', 'Chronic diseases', 'Heavy menstrual bleeding', 'Pregnancy', 'Intestinal disorders']
  },
  {
    id: 'c_030',
    name: 'Hypothyroidism',
    symptoms: ['s_5', 's_211', 's_213', 's_23', 's_95', 's_160', 's_31'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasKeySymptoms = selectedSymptoms.includes('s_5') && selectedSymptoms.includes('s_211');
      const keySymptomBoost = hasKeySymptoms ? 0.3 : 0;
      let sexAdjustment = 1.0;
      if (userData && userData.sex && userData.sex.toLowerCase() === 'female') sexAdjustment = 1.5;
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * sexAdjustment);
    },
    triage_level: 'consultation',
    description: 'A condition in which your thyroid gland doesn\'t produce enough of certain crucial hormones.',
    treatment: 'Thyroid hormone replacement medication (levothyroxine).',
    risk_factors: ['Female sex', 'Age (over 60)', 'Autoimmune disease', 'Family history', 'Radiation to neck/chest']
  },
  {
    id: 'c_031',
    name: 'Hyperthyroidism',
    symptoms: ['s_20', 's_5', 's_18', 's_41', 's_214', 's_24', 's_84'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasKeySymptoms = selectedSymptoms.includes('s_20') && selectedSymptoms.includes('s_18');
      const keySymptomBoost = hasKeySymptoms ? 0.3 : 0;
      let sexAdjustment = 1.0;
      if (userData && userData.sex && userData.sex.toLowerCase() === 'female') sexAdjustment = 1.5;
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)) * sexAdjustment);
    },
    triage_level: 'consultation',
    description: 'A condition in which your thyroid gland produces too much of the hormone thyroxine.',
    treatment: 'Antithyroid medications, radioactive iodine therapy, surgery.',
    risk_factors: ['Female sex', 'Family history', 'Other autoimmune conditions', 'Pregnancy']
  },
  {
    id: 'c_032',
    name: 'Allergic Reaction (Moderate)',
    symptoms: ['s_16', 's_151', 's_152', 's_54', 's_10'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 5;
      const specificity = matchCount / selectedSymptoms.length;
      const hasSkinSymptoms = selectedSymptoms.includes('s_16') || selectedSymptoms.includes('s_152');
      const hasRespiratorySymptoms = selectedSymptoms.includes('s_54') || selectedSymptoms.includes('s_10');
      const keySymptomBoost = (hasSkinSymptoms && hasRespiratorySymptoms) ? 0.4 : 0;
      return Math.min(0.98, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'emergency',
    description: 'An immune system response to a foreign substance that\'s not typically harmful to your body.',
    treatment: 'Antihistamines, corticosteroids, epinephrine (if severe), avoiding allergen.',
    risk_factors: ['Known allergies', 'Asthma', 'Family history']
  },
  {
    id: 'c_033',
    name: 'Psoriasis',
    symptoms: ['s_16', 's_155', 's_156', 's_151', 's_161'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 5;
      const specificity = matchCount / selectedSymptoms.length;
      const hasTypicalSkinSymptoms = selectedSymptoms.includes('s_16') && selectedSymptoms.includes('s_155') && selectedSymptoms.includes('s_156');
      const keySymptomBoost = hasTypicalSkinSymptoms ? 0.3 : 0;
      return Math.min(0.90, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'A chronic skin disease that causes red, itchy scaly patches, most commonly on the knees, elbows, trunk and scalp.',
    treatment: 'Topical creams, light therapy, oral or injected medications.',
    risk_factors: ['Family history', 'Viral and bacterial infections', 'Stress', 'Smoking', 'Obesity']
  },
  {
    id: 'c_034',
    name: 'Eczema (Atopic Dermatitis)',
    symptoms: ['s_151', 's_154', 's_16', 's_155', 's_156'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 5;
      const specificity = matchCount / selectedSymptoms.length;
      const hasItchyDrySkin = selectedSymptoms.includes('s_151') && selectedSymptoms.includes('s_154');
      const keySymptomBoost = hasItchyDrySkin ? 0.2 : 0;
      return Math.min(0.85, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'self_care',
    description: 'A condition that causes dry, itchy and inflamed skin. It\'s common in young children but can occur at any age.',
    treatment: 'Moisturizers, topical corticosteroids, avoiding irritants.',
    risk_factors: ['Family history of eczema, allergies, asthma', 'Environmental factors']
  },
  {
    id: 'c_035',
    name: 'Conjunctivitis (Pink Eye)',
    symptoms: ['s_171', 's_172', 's_173', 's_174', 's_178'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 5;
      const specificity = matchCount / selectedSymptoms.length;
      const hasRedWateryEyes = selectedSymptoms.includes('s_171') && selectedSymptoms.includes('s_173');
      const keySymptomBoost = hasRedWateryEyes ? 0.3 : 0;
      return Math.min(0.90, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'self_care',
    description: 'Inflammation or infection of the outer membrane of the eyeball and the inner eyelid.',
    treatment: 'Eye drops (antibiotic if bacterial, antihistamine if allergic), warm/cold compresses, avoiding irritants.',
    risk_factors: ['Exposure to infected individuals', 'Allergies', 'Contact lens use']
  },
  {
    id: 'c_036',
    name: 'Otitis Media (Middle Ear Infection)',
    symptoms: ['s_35', 's_2', 's_19', 's_7', 's_9'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 5;
      const specificity = matchCount / selectedSymptoms.length;
      const hasEarPainAndFever = selectedSymptoms.includes('s_35') && selectedSymptoms.includes('s_2');
      const keySymptomBoost = hasEarPainAndFever ? 0.4 : 0;
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'An infection of the middle ear, the space behind the eardrum.',
    treatment: 'Antibiotics, pain relievers, decongestants.',
    risk_factors: ['Age (young children)', 'Eustachian tube dysfunction', 'Recent cold or flu', 'Allergies', 'Exposure to smoke']
  },
  {
    id: 'c_037',
    name: 'Severe Allergic Reaction (Anaphylaxis)',
    symptoms: ['s_16', 's_151', 's_152', 's_153', 's_17', 's_18', 's_19', 's_20', 's_21', 's_22'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 10;
      const specificity = matchCount / selectedSymptoms.length;
      const hasKeySymptoms = selectedSymptoms.includes('s_16') && selectedSymptoms.includes('s_151') && selectedSymptoms.includes('s_152');
      const keySymptomBoost = hasKeySymptoms ? 0.6 : 0;
      return Math.min(0.99, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'emergency',
    description: 'A severe, potentially life-threatening allergic reaction that can cause shock, a sudden drop in blood pressure, and trouble breathing.',
    treatment: 'Epinephrine (adrenaline) injection, antihistamines, corticosteroids, emergency medical care.',
    risk_factors: ['Known allergies', 'Previous anaphylactic reaction', 'Asthma', 'Family history of allergies']
  },
  {
    id: 'c_038',
    name: 'Sinusitis',
    symptoms: ['s_1', 's_37', 's_36', 's_4', 's_5', 's_12', 's_60'], // Added s_60 (Stuffy Nose)
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7; // Updated to 7 symptoms
      const specificity = matchCount / selectedSymptoms.length;
      // Accept either s_37 (Nasal Congestion) or s_60 (Stuffy Nose) for congestion
      const hasFacialPain = selectedSymptoms.includes('s_1');
      const hasCongestion = selectedSymptoms.includes('s_37') || selectedSymptoms.includes('s_60');
      const hasFacialPainAndCongestion = hasFacialPain && hasCongestion;
      const keySymptomBoost = hasFacialPainAndCongestion ? 0.3 : 0;
      return Math.min(0.90, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'An inflammation or swelling of the tissue lining the sinuses.',
    treatment: 'Nasal corticosteroids, saline nasal sprays, decongestants, antibiotics (if bacterial).',
    risk_factors: ['Allergies', 'Nasal polyps', 'Asthma', 'Weakened immune system', 'Smoking']
  },
  {
    id: 'c_039', // Changed from c_038 to c_039
    name: 'Food Poisoning',
    symptoms: ['s_6', 's_7', 's_8', 's_12', 's_2', 's_17'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasAcuteGI = selectedSymptoms.includes('s_6') && selectedSymptoms.includes('s_7') && selectedSymptoms.includes('s_8');
      const keySymptomBoost = hasAcuteGI ? 0.5 : 0;
      return Math.min(0.98, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'emergency',
    description: 'Illness caused by food contaminated with bacteria, viruses, parasites, or toxins.',
    treatment: 'Rest, fluids, electrolyte replacement, antibiotics (if bacterial and severe).',
    risk_factors: ['Consumption of raw/undercooked food', 'Unpasteurized dairy', 'Contaminated water']
  },
  {
    id: 'c_040',
    name: 'Meningitis',
    symptoms: ['s_1', 's_2', 's_112', 's_113', 's_7', 's_16', 's_49'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasTriad = selectedSymptoms.includes('s_1') && selectedSymptoms.includes('s_2') && selectedSymptoms.includes('s_112'); // Headache, Fever, Stiff Neck
      const keySymptomBoost = hasTriad ? 0.7 : 0;
      return Math.min(0.99, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'emergency',
    description: 'An inflammation of the fluid and membranes (meninges) surrounding your brain and spinal cord.',
    treatment: 'Antibiotics (bacterial), antiviral medications (viral), corticosteroids, hospitalization.',
    risk_factors: ['Age (infants, young adults)', 'Weakened immune system', 'Living in communal settings']
  },
  {
    id: 'c_041',
    name: 'Concussion',
    symptoms: ['s_1', 's_9', 's_6', 's_34', 's_49', 's_122', 's_21'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasHeadInjurySymptoms = selectedSymptoms.includes('s_1') && selectedSymptoms.includes('s_9');
      const keySymptomBoost = hasHeadInjurySymptoms ? 0.4 : 0;
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'A traumatic brain injury that affects your brain function.',
    treatment: 'Rest (physical and cognitive), avoiding aggravating activities, gradual return to activity.',
    risk_factors: ['Participation in contact sports', 'Falls', 'Motor vehicle accidents', 'Previous concussions']
  },
  {
    id: 'c_042',
    name: 'Tonsillitis',
    symptoms: ['s_4', 's_2', 's_40', 's_186', 's_187', 's_19'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasKeySymptoms = selectedSymptoms.includes('s_4') && selectedSymptoms.includes('s_40') && selectedSymptoms.includes('s_187');
      const keySymptomBoost = hasKeySymptoms ? 0.4 : 0;
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'Inflammation of the tonsils, two oval-shaped pads of tissue at the back of the throat.',
    treatment: 'Antibiotics (if bacterial), pain relievers, rest, fluids, tonsillectomy (recurrent cases).',
    risk_factors: ['Age (children)', 'Exposure to germs', 'Frequent colds']
  },
  {
    id: 'c_043',
    name: 'Hypoglycemia (Low Blood Sugar)',
    symptoms: ['s_42', 's_18', 's_26', 's_44', 's_49', 's_120'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasCriticalSymptoms = selectedSymptoms.includes('s_42') && selectedSymptoms.includes('s_18');
      const keySymptomBoost = hasCriticalSymptoms ? 0.5 : 0;
      return Math.min(0.98, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'emergency',
    description: 'A condition caused by a drop in blood sugar levels, usually below 70 mg/dL.',
    treatment: 'Ingesting quick-acting carbohydrates, glucose tablets, glucagon injection (severe).',
    risk_factors: ['Diabetes (especially insulin/certain medications)', 'Excessive alcohol consumption', 'Certain tumors']
  },
  {
    id: 'c_044',
    name: 'Vertigo (Benign Paroxysmal Positional Vertigo - BPPV)',
    symptoms: ['s_129', 's_9', 's_6', 's_7', 's_47'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 5;
      const specificity = matchCount / selectedSymptoms.length;
      const hasSpinningSensation = selectedSymptoms.includes('s_129') && selectedSymptoms.includes('s_9');
      const keySymptomBoost = hasSpinningSensation ? 0.4 : 0;
      return Math.min(0.90, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'A sudden sensation that you\'re spinning or that the inside of your head is spinning.',
    treatment: 'Epley maneuver, canalith repositioning procedures, medications for nausea.',
    risk_factors: ['Age', 'Head trauma', 'Inner ear disorders']
  },
  {
    id: 'c_045',
    name: 'Pneumothorax (Collapsed Lung)',
    symptoms: ['s_10', 's_11', 's_55', 's_56', 's_59'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 5;
      const specificity = matchCount / selectedSymptoms.length;
      const hasAcuteChestPainSOB = selectedSymptoms.includes('s_10') && selectedSymptoms.includes('s_11');
      const keySymptomBoost = hasAcuteChestPainSOB ? 0.6 : 0;
      return Math.min(0.99, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'emergency',
    description: 'A collapsed lung occurs when air leaks into the space between your lung and chest wall.',
    treatment: 'Observation (small pneumothorax), needle aspiration, chest tube insertion, surgery.',
    risk_factors: ['Smoking', 'Lung disease (COPD, asthma)', 'Chest injury', 'Genetic factors']
  },
  {
    id: 'c_046',
    name: 'Deep Vein Thrombosis (DVT)',
    symptoms: ['s_25', 's_15', 's_75', 's_133', 's_76'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 5;
      const specificity = matchCount / selectedSymptoms.length;
      const hasUnilateralLegSwellingPain = selectedSymptoms.includes('s_25') && selectedSymptoms.includes('s_15') && selectedSymptoms.includes('s_75');
      const keySymptomBoost = hasUnilateralLegSwellingPain ? 0.5 : 0;
      return Math.min(0.98, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'emergency',
    description: 'A blood clot in a deep vein, usually in the leg.',
    treatment: 'Anticoagulants (blood thinners), compression stockings, surgery (rarely).',
    risk_factors: ['Prolonged immobility', 'Surgery', 'Cancer', 'Obesity', 'Pregnancy', 'Smoking', 'Family history']
  },
  {
    id: 'c_047',
    name: 'Cellulitis',
    symptoms: ['s_16', 's_133', 's_134', 's_25', 's_2', 's_12'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasRedWarmSwollenSkin = selectedSymptoms.includes('s_16') && selectedSymptoms.includes('s_133') && selectedSymptoms.includes('s_134') && selectedSymptoms.includes('s_25');
      const keySymptomBoost = hasRedWarmSwollenSkin ? 0.5 : 0;
      return Math.min(0.98, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'A common, potentially serious bacterial skin infection.',
    treatment: 'Antibiotics.',
    risk_factors: ['Skin injury', 'Weakened immune system', 'Chronic swelling of arms/legs', 'Obesity']
  },
  {
    id: 'c_048',
    name: 'Shingles (Herpes Zoster)',
    symptoms: ['s_16', 's_153', 's_15', 's_13', 's_27', 's_111'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasUnilateralPainfulRash = selectedSymptoms.includes('s_16') && selectedSymptoms.includes('s_153') && selectedSymptoms.includes('s_15');
      const keySymptomBoost = hasUnilateralPainfulRash ? 0.4 : 0;
      return Math.min(0.95, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'consultation',
    description: 'A viral infection that causes a painful rash, typically presenting as a single stripe of blisters on one side of the body or face.',
    treatment: 'Antiviral medications, pain relievers, topical creams.',
    risk_factors: ['Previous chickenpox infection', 'Age (over 50)', 'Weakened immune system', 'Stress']
  },
  {
    id: 'c_049',
    name: 'Urgent Hypertension',
    symptoms: ['s_111', 's_34', 's_10', 's_184', 's_6', 's_7'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasSevereHeadacheAndOtherSymptoms = selectedSymptoms.includes('s_111') && (selectedSymptoms.includes('s_34') || selectedSymptoms.includes('s_10'));
      const keySymptomBoost = hasSevereHeadacheAndOtherSymptoms ? 0.7 : 0;
      return Math.min(0.99, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'emergency',
    description: 'Severely elevated blood pressure (typically >180/120 mmHg) without signs of target organ damage.',
    treatment: 'Emergency medical evaluation, oral blood pressure medications to gradually lower BP.',
    risk_factors: ['Untreated or poorly controlled hypertension']
  },
  {
    id: 'c_050',
    name: 'Anaphylaxis',
    symptoms: ['s_10', 's_54', 's_152', 's_25', 's_42', 's_7', 's_120'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 7;
      const specificity = matchCount / selectedSymptoms.length;
      const hasMultiSystemSymptoms = (selectedSymptoms.includes('s_10') || selectedSymptoms.includes('s_54')) && (selectedSymptoms.includes('s_152') || selectedSymptoms.includes('s_25'));
      const keySymptomBoost = hasMultiSystemSymptoms ? 0.8 : 0;
      return Math.min(0.99, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'emergency',
    description: 'A severe, potentially life-threatening allergic reaction.',
    treatment: 'Immediate epinephrine injection, emergency medical care, antihistamines, corticosteroids.',
    risk_factors: ['Previous severe allergic reaction', 'Known allergies (food, insect stings, medications)']
  },
  {
    id: 'c_051',
    name: 'Pancreatitis',
    symptoms: ['s_12', 's_6', 's_7', 's_2', 's_18', 's_106'],
    probability_function: (matchCount: number, selectedSymptoms: string[], userData) => {
      const coverage = matchCount / 6;
      const specificity = matchCount / selectedSymptoms.length;
      const hasSevereUpperAbdominalPain = selectedSymptoms.includes('s_12') && selectedSymptoms.includes('s_106');
      const keySymptomBoost = hasSevereUpperAbdominalPain ? 0.5 : 0;
      return Math.min(0.98, ((coverage * 0.7 + keySymptomBoost) * (specificity * 0.6 + 0.4)));
    },
    triage_level: 'emergency',
    description: 'Inflammation of the pancreas, a gland located behind the stomach that produces enzymes and hormones.',
    treatment: 'Hospitalization, IV fluids, pain medication, antibiotics (if infection), dietary restrictions.',
    risk_factors: ['Gallstones', 'Alcohol abuse', 'High triglycerides', 'Abdominal surgery', 'Certain medications']
  }
];

// Emergency symptoms that require immediate attention
const emergencySymptoms = [
  's_9',  // Chest Pain
  's_35', // Difficulty Speaking
  's_36', // Weakness (especially on one side)
  's_34', // Confusion
  's_47', // Fainting
  's_37',  // Stiff Neck with Fever (meningitis concern)
  's_462', // Syncope (Fainting)
  's_423', // Facial Drooping
  's_424', // Arm Weakness
  's_431', // Dysarthria (Slurred Speech)
  's_432', // Aphasia (Language Difficulty)
  's_464', // Angina (Chest Pain)
  's_455', // Hemoptysis (Coughing Blood)
  's_478', // Hematuria (Blood in Urine)
  's_148'  // Suicidal Thoughts
];

// Age-specific risk adjustments
const ageRiskAdjustments = (condition: string, age: number) => {
  switch(condition) {
    case 'c_007': // COVID-19
      if (age > 60) return 1.3;
      if (age > 40) return 1.1;
      return 1.0;
    case 'c_004': // Pneumonia
      if (age > 65 || age < 5) return 1.4;
      return 1.0;
    case 'c_016': // Stroke
      if (age > 70) return 1.5;
      if (age > 55) return 1.3;
      if (age > 40) return 1.1;
      return 0.8;
    case 'c_017': // Heart Attack
      if (age > 65) return 1.5;
      return 1.0;
    // Add more conditions and their age adjustments here
    default:
      return 1.0;
  }
};

// Sex-specific risk adjustments
const sexRiskAdjustments = (condition: string, sex: string) => {
  switch(condition) {
    case 'c_007': // COVID-19
      if (sex === 'female') return 1.5;
      return 1.0;
    case 'c_013': // Urinary Tract Infection (UTI)
      if (sex === 'female') return 1.5;
      if (sex === 'male') return 0.7;
      return 1.0;
    case 'c_014': // Diabetes Mellitus
      if (sex === 'female') return 1.3;
      return 1.0;
    case 'c_018': // Irritable Bowel Syndrome (IBS)
      if (sex === 'female') return 1.2;
      if (sex === 'male') return 0.9;
      return 1.0;
    // Add more conditions and their sex adjustments here
    default:
      return 1.0;
  }
};

// Helper function to check for emergency symptoms
const hasEmergencySymptoms = (selectedSymptoms: string[]): boolean => {
  return selectedSymptoms.some(symptom => emergencySymptoms.includes(symptom));
};

// Calculate diagnosis based on symptoms and user data
const calculateDiagnosis = (selectedSymptoms: string[], userData?: { age?: number; sex?: string }): DiagnosisResult[] => {
  try {
    // Check for emergency symptoms first
    if (hasEmergencySymptoms(selectedSymptoms)) {
      return [{
        condition: 'Emergency Condition',
        probability: '95%',
        triage_level: 'emergency',
        description: 'Your symptoms may indicate a medical emergency. Please seek immediate medical attention.',
        treatment: 'Seek immediate emergency medical care.',
        risk_factors: []
      }];
    }

    // Calculate probabilities for each condition
    const conditionResults: DiagnosisResult[] = conditions.map(condition => {
      const matchingSymptoms = condition.symptoms.filter(symptom =>
        selectedSymptoms.includes(symptom)
      );
      const matchCount = matchingSymptoms.length;

      if (matchCount === 0) {
        return {
          condition: condition.name,
          probability: '0%',
          triage_level: condition.triage_level,
          description: condition.description,
          treatment: condition.treatment || 'Consult with healthcare provider',
          risk_factors: condition.risk_factors || []
        };
      }

      // Calculate base probability using the condition's probability function
      let probability = condition.probability_function(matchCount, selectedSymptoms, userData);

      // Apply age and sex adjustments if user data is available
      if (userData) {
        if (userData.age) {
          const ageAdjustment = ageRiskAdjustments(condition.id, userData.age);
          probability *= ageAdjustment;
        }

        if (userData.sex) {
          const sexAdjustment = sexRiskAdjustments(condition.id, userData.sex);
          probability *= sexAdjustment;
        }
      }

      // Ensure probability doesn't exceed 100%
      probability = Math.min(probability, 1.0);

      return {
        condition: condition.name,
        probability: `${Math.round(probability * 100)}%`,
        triage_level: condition.triage_level,
        description: condition.description,
        treatment: condition.treatment || 'Consult with healthcare provider',
        risk_factors: condition.risk_factors || []
      };
    });

    // Sort conditions by probability in descending order
    conditionResults.sort((a, b) => parseFloat(b.probability) - parseFloat(a.probability));

    // Return the top 3 conditions
    return conditionResults.slice(0, 3);
  } catch (error) {
    console.error("Error calculating diagnosis:", error);
    throw new Error("Unable to calculate diagnosis. Please try again later.");
  }
};

// Define diagnosis result interface
interface DiagnosisResult {
  condition: string;
  probability: string;
  triage_level: 'emergency' | 'consultation' | 'self_care';
  description: string;
  treatment: string;
  risk_factors: string[];
}

// Define symptom interface
interface Symptom {
  id: string;
  name: string;
}

// Define condition interface
interface Condition {
  id: string;
  name: string;
  symptoms: string[];
  probability_function: (matchCount: number, selectedSymptoms: string[], userData?: { age?: number; sex?: string }) => number;
  triage_level: 'emergency' | 'consultation' | 'self_care';
  description: string;
  treatment: string;
  risk_factors: string[];
}

// Export the functions that the symptom checker page expects
export const getSymptoms = async (): Promise<Symptom[]> => {
  try {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return symptoms;
  } catch (error) {
    console.error("Error fetching symptoms:", error);
    throw new Error("Unable to retrieve symptoms. Please try again later.");
  }
};

export const getDiagnosis = async (symptomIds: string[], sex: string, age: number) => {
  try {
    // Validate inputs
    if (!symptomIds || !Array.isArray(symptomIds) || symptomIds.length === 0) {
      throw new Error("Please select at least one symptom");
    }

    console.log("Selected symptoms:", symptomIds);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Create userData object for probability calculations
    const userData = { 
      age: typeof age === 'number' ? age : parseInt(age, 10) || 30, // Ensure age is a number
      sex: typeof sex === 'string' ? sex.toLowerCase() : 'unknown'  // Ensure sex is a lowercase string
    };

    console.log("User data:", userData);

    // Calculate condition matches and probabilities
    const conditionResults = conditions.map(condition => {
      // Find matching symptoms
      const matchingSymptoms = condition.symptoms.filter(symptom => 
        symptomIds.includes(symptom)
      );
      const matchCount = matchingSymptoms.length;
      
      console.log(`Condition: ${condition.name}, Matching symptoms: ${matchCount}/${condition.symptoms.length}`);

      // Default values
      let probability = 0;
      
      // Only calculate if there are matching symptoms
      if (matchCount > 0) {
        try {
          // Call the probability function with the correct parameters
          if (typeof condition.probability_function === 'function') {
            // The key fix: pass symptomIds as the selectedSymptoms parameter
            probability = condition.probability_function(matchCount, symptomIds, userData);

            // Ensure probability is a valid number
            if (isNaN(probability)) {
              console.error(`NaN probability for ${condition.name}, using fallback calculation`);
              // Fallback calculation if the function returns NaN
              probability = matchCount / condition.symptoms.length;
            }

            // Apply age and sex adjustments if functions exist
            if (typeof ageRiskAdjustments === 'function') {
              const ageAdjustment = ageRiskAdjustments(condition.id, age);
              if (!isNaN(ageAdjustment) && isFinite(ageAdjustment)) {
                probability *= ageAdjustment;
              }
            }

            if (typeof sexRiskAdjustments === 'function') {
              const sexAdjustment = sexRiskAdjustments(condition.id, sex);
              if (!isNaN(sexAdjustment) && isFinite(sexAdjustment)) {
                probability *= sexAdjustment;
              }
            }

            // Ensure probability is between 0 and 0.99
            probability = Math.max(0, Math.min(0.99, probability));
          } else {
            // Fallback if no probability function exists
            probability = matchCount / condition.symptoms.length;
          }
          
          console.log(`Final probability for ${condition.name}: ${probability}`);
        } catch (err) {
          console.error(`Error calculating probability for ${condition.name}:`, err);
          // Fallback calculation if an error occurs
          probability = matchCount / condition.symptoms.length;
        }
      }
      
      // Format probability as percentage string
      const probabilityDisplay = `${Math.round(probability * 100)}%`;
      
      // Determine probability label
      let probabilityLabel = "Low";
      if (probability >= 0.7) probabilityLabel = "High";
      else if (probability >= 0.4) probabilityLabel = "Medium";
      
      return {
        id: condition.id,
        name: condition.name,
        common_name: condition.name,
        probability: probability, // Numeric value for sorting
        probability_display: probabilityDisplay, // String for display
        probability_label: probabilityLabel, // Label for UI
        triage_level: condition.triage_level || 'consultation',
        description: condition.description || '',
        treatment: condition.treatment || '',
        risk_factors: condition.risk_factors || []
      };
    });

    // Sort conditions by probability in descending order
    conditionResults.sort((a, b) => b.probability - a.probability);

    // Determine overall triage level based on highest probability condition
    const topCondition = conditionResults[0];
    const triageLevel = topCondition && topCondition.probability >= 0.7 
      ? topCondition.triage_level 
      : 'consultation';

    // Return in the format expected by the symptom checker page
    return {
      conditions: conditionResults.slice(0, 5).map(condition => ({
        ...condition,
        probability: condition.probability, // Keep numeric value for calculations
        probability_display: condition.probability_display, // String format for display
        probability_percentage: Math.round(condition.probability * 100) // Percentage as number
      })),
      triage: {
        triage_level: triageLevel,
        serious: []
      }
    };
  } catch (error) {
    console.error("Error calculating diagnosis:", error);
    throw new Error("Unable to calculate diagnosis. Please try again later.");
  }
};

// Also export the service object for compatibility
export const mockSymptomCheckerService = {
  checkSymptoms: async (selectedSymptoms: string[], userData?: { age?: number; sex?: string }): Promise<DiagnosisResult[]> => {
    return calculateDiagnosis(selectedSymptoms, userData);
  },

  getSymptoms: async (): Promise<Symptom[]> => {
    return symptoms;
  },

  getConditions: async (): Promise<Condition[]> => {
    return conditions;
  }
};





























