import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { ThemeProvider } from '@mui/material/styles'
import CssBaseline from '@mui/material/CssBaseline'
import theme from './theme'
import { FirebaseProvider } from './contexts/FirebaseContext'

try {
  ReactDOM.createRoot(document.getElementById('root')!).render(
    <React.StrictMode>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <FirebaseProvider>
          <App />
        </FirebaseProvider>
      </ThemeProvider>
    </React.StrictMode>,
  )
} catch (error) {
  console.error("Error rendering application:", error);
  // Render a fallback UI
  ReactDOM.createRoot(document.getElementById('root')!).render(
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Something went wrong</h1>
      <p>The application failed to load. Please check the console for more details.</p>
    </div>
  )
}



