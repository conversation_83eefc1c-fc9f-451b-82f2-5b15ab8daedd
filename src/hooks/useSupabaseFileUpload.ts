import { useState } from 'react';
import { uploadFile, getFileUrl, validateFile, generateFilePath, STORAGE_BUCKETS } from '../services/supabase';

interface UploadState {
  isUploading: boolean;
  progress: number;
  error: string | null;
  fileUrl: string | null;
  filePath: string | null;
}

export const useSupabaseFileUpload = () => {
  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    progress: 0,
    error: null,
    fileUrl: null,
    filePath: null
  });

  const uploadMedia = async (userId: string, file: File, category: keyof typeof STORAGE_BUCKETS) => {
    setUploadState({ ...uploadState, isUploading: true, progress: 0, error: null });
    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      setUploadState({ ...uploadState, isUploading: false, error: validation.error || 'Invalid file' });
      return { fileUrl: null, filePath: null, error: validation.error };
    }
    try {
      const filePath = generateFilePath(userId, category, file.name);
      // Supabase upload
      await uploadFile(STORAGE_BUCKETS[category], filePath, file);
      const url = getFileUrl(STORAGE_BUCKETS[category], filePath);
      setUploadState({ isUploading: false, progress: 100, error: null, fileUrl: url, filePath });
      return { fileUrl: url, filePath };
    } catch (error: unknown) {
      const errMsg = error instanceof Error ? error.message : 'Upload failed';
      setUploadState({ ...uploadState, isUploading: false, error: errMsg, fileUrl: null, filePath: null });
      return { fileUrl: null, filePath: null, error: errMsg };
    }
  };

  const resetUploadState = () => {
    setUploadState({ isUploading: false, progress: 0, error: null, fileUrl: null, filePath: null });
  };

  return { uploadState, uploadMedia, resetUploadState };
};
