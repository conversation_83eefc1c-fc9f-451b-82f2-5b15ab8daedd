import { useEffect, useState } from 'react';
import { collection, onSnapshot, query, where, orderBy } from 'firebase/firestore';
import { db } from '../services/firebase';

export interface Notification {
  id?: string;
  userId: string;
  type: 'appointment' | 'medication' | 'health_tip' | 'record';
  title: string;
  message: string;
  read: boolean;
  createdAt: Date;
}

export const useNotifications = (userId: string) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  useEffect(() => {
    if (!userId) return;
    const q = query(collection(db, 'notifications'), where('userId', '==', userId), orderBy('createdAt', 'desc'));
    const unsub = onSnapshot(q, (snap) => {
      setNotifications(
        snap.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.() || new Date()
        } as Notification))
      );
    });
    return () => unsub();
  }, [userId]);
  return notifications;
};

// Ensure this file has a .ts extension, not .js, and is imported using a relative path (not a URL) in your dashboard code.
// If you are using Vite or a similar dev server, make sure the import is:
// import { useNotifications } from '../../hooks/useNotifications';
// and not from a URL or with a .js extension.
