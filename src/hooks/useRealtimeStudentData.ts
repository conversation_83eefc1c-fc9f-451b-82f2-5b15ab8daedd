import { useEffect, useState } from 'react';
import { collection, onSnapshot, query, where, orderBy } from 'firebase/firestore';
import { db } from '../services/firebase';
import type { HealthMetric } from '../features/healthMetrics/healthMetricsService';
import type { Medication } from '../features/medications/medicationsService';
import type { MedicalRecord } from '../services/medicalRecordService';

export const useRealtimeHealthMetrics = (userId: string) => {
  const [metrics, setMetrics] = useState<HealthMetric[]>([]);
  useEffect(() => {
    if (!userId) return;
    const q = query(collection(db, 'healthMetrics'), where('userId', '==', userId), orderBy('date', 'desc'));
    const unsub = onSnapshot(q, (snap) => {
      setMetrics(snap.docs.map(doc => ({ id: doc.id, ...doc.data() } as HealthMetric)));
    });
    return () => unsub();
  }, [userId]);
  return metrics;
};

export const useRealtimeMedications = (userId: string) => {
  const [medications, setMedications] = useState<Medication[]>([]);
  useEffect(() => {
    if (!userId) return;
    const q = query(collection(db, 'medications'), where('userId', '==', userId));
    const unsub = onSnapshot(q, (snap) => {
      setMedications(snap.docs.map(doc => ({ id: doc.id, ...doc.data() } as Medication)));
    });
    return () => unsub();
  }, [userId]);
  return medications;
};

export const useRealtimeMedicalRecords = (userId: string) => {
  const [records, setRecords] = useState<MedicalRecord[]>([]);
  useEffect(() => {
    if (!userId) return;
    const q = query(collection(db, 'medicalRecords'), where('studentId', '==', userId), orderBy('date', 'desc'));
    const unsub = onSnapshot(q, (snap) => {
      setRecords(snap.docs.map(doc => ({ id: doc.id, ...doc.data() } as MedicalRecord)));
    });
    return () => unsub();
  }, [userId]);
  return records;
};
