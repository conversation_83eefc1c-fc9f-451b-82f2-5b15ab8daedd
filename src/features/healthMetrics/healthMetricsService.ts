import { addDocument, queryDocuments } from '../../services/firestoreService';

export interface HealthMetric {
  id?: string;
  userId: string;
  type: 'weight' | 'bloodPressure' | 'heartRate' | 'activity';
  value?: number;
  systolic?: number;
  diastolic?: number;
  steps?: number;
  calories?: number;
  date: string;
  createdAt?: Date;
  updatedAt?: Date;
}

const COLLECTION = 'healthMetrics';

export const createHealthMetric = async (metric: Omit<HealthMetric, 'id' | 'createdAt' | 'updatedAt'>) => {
  return addDocument<HealthMetric>(COLLECTION, metric);
};

export const getHealthMetrics = async (userId: string) => {
  return queryDocuments<HealthMetric>(COLLECTION, [], true).then(metrics =>
    metrics.filter(m => m.userId === userId)
  );
};
