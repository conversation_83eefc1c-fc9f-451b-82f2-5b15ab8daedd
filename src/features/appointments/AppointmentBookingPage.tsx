import React, { useState, useEffect } from 'react';
import { 
  Container, Typography, Box, Paper, Grid, Button, 
  FormControl, InputLabel, Select, MenuItem, TextField,
  Card, CardContent, CardActions, Chip, Divider,
  Dialog, DialogTitle, DialogContent, DialogActions,
  CircularProgress, Alert, LinearProgress
} from '@mui/material';
import { 
  CalendarMonth as CalendarIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { useFirebase } from '../../contexts/FirebaseContext';
import Layout from '../../components/layout/Layout';

const AppointmentBookingPage = () => {
  const [doctors, setDoctors] = useState([]);
  const [selectedDoctor, setSelectedDoctor] = useState('');
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [appointmentReason, setAppointmentReason] = useState('');
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [bookingSuccess, setBookingSuccess] = useState(false);
  const [availableTimes, setAvailableTimes] = useState([]);
  const [upcomingAppointments, setUpcomingAppointments] = useState([]);
  const { currentUser } = useFirebase();

  useEffect(() => {
    // Mock data for doctors
    setTimeout(() => {
      setDoctors([
        { id: 'd1', name: 'Dr. Sarah Johnson', specialty: 'General Physician', availability: ['2023-06-01', '2023-06-02', '2023-06-05'] },
        { id: 'd2', name: 'Dr. Michael Chen', specialty: 'Dermatologist', availability: ['2023-06-02', '2023-06-03', '2023-06-06'] },
        { id: 'd3', name: 'Dr. Emily Rodriguez', specialty: 'Psychiatrist', availability: ['2023-06-01', '2023-06-04', '2023-06-07'] }
      ]);
      
      // Mock data for upcoming appointments
      setUpcomingAppointments([
        { id: 'a1', doctor: 'Dr. Sarah Johnson', date: '2023-05-28', time: '10:00 AM', reason: 'Annual checkup' },
        { id: 'a2', doctor: 'Dr. Michael Chen', date: '2023-06-15', time: '2:30 PM', reason: 'Skin rash follow-up' }
      ]);
      
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    // Update available times when doctor or date changes
    if (selectedDoctor && selectedDate) {
      // Mock available time slots
      const times = [
        '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM',
        '11:00 AM', '11:30 AM', '1:00 PM', '1:30 PM',
        '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM'
      ];
      
      // Randomly remove some times to simulate booked slots
      const availableTimes = times.filter(() => Math.random() > 0.3);
      setAvailableTimes(availableTimes);
    } else {
      setAvailableTimes([]);
    }
  }, [selectedDoctor, selectedDate]);

  const handleDoctorChange = (event) => {
    setSelectedDoctor(event.target.value);
    setSelectedDate('');
    setSelectedTime('');
  };

  const handleDateChange = (event) => {
    setSelectedDate(event.target.value);
    setSelectedTime('');
  };

  const handleTimeChange = (event) => {
    setSelectedTime(event.target.value);
  };

  const handleReasonChange = (event) => {
    setAppointmentReason(event.target.value);
  };

  const handleBookAppointment = () => {
    setOpenDialog(true);
  };

  const handleConfirmBooking = () => {
    // Mock booking process
    setLoading(true);
    setTimeout(() => {
      // Add the new appointment to the list
      const newAppointment = {
        id: `a${Date.now()}`,
        doctor: doctors.find(d => d.id === selectedDoctor)?.name || '',
        date: selectedDate,
        time: selectedTime,
        reason: appointmentReason
      };
      
      setUpcomingAppointments([...upcomingAppointments, newAppointment]);
      setBookingSuccess(true);
      setLoading(false);
      
      // Reset form
      setSelectedDoctor('');
      setSelectedDate('');
      setSelectedTime('');
      setAppointmentReason('');
      
      // Close dialog after a delay
      setTimeout(() => {
        setOpenDialog(false);
        setBookingSuccess(false);
      }, 2000);
    }, 1500);
  };

  const handleCancelDialog = () => {
    setOpenDialog(false);
  };

  const getAvailableDates = () => {
    if (!selectedDoctor) return [];
    
    const doctor = doctors.find(d => d.id === selectedDoctor);
    if (!doctor) return [];
    
    // Generate dates for the next 14 days
    const dates = [];
    const today = new Date();
    
    for (let i = 0; i < 14; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      
      // Skip weekends
      if (date.getDay() === 0 || date.getDay() === 6) continue;
      
      const dateString = date.toISOString().split('T')[0];
      dates.push(dateString);
    }
    
    // Filter dates based on doctor's availability
    return dates.filter(date => doctor.availability.includes(date));
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Book an Appointment
        </Typography>
        
        <Grid container spacing={4}>
          {/* Booking Form */}
          <Grid item xs={12} md={7}>
            <Paper sx={{ p: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom>
                Select a Doctor and Time
              </Typography>
              
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <Box component="form" sx={{ mt: 2 }}>
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel id="doctor-select-label">Doctor</InputLabel>
                    <Select
                      labelId="doctor-select-label"
                      id="doctor-select"
                      value={selectedDoctor}
                      label="Doctor"
                      onChange={handleDoctorChange}
                      startAdornment={
                        <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }
                    >
                      {doctors.map((doctor) => (
                        <MenuItem key={doctor.id} value={doctor.id}>
                          {doctor.name} - {doctor.specialty}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  
                  <FormControl fullWidth sx={{ mb: 3 }} disabled={!selectedDoctor}>
                    <InputLabel id="date-select-label">Date</InputLabel>
                    <Select
                      labelId="date-select-label"
                      id="date-select"
                      value={selectedDate}
                      label="Date"
                      onChange={handleDateChange}
                      startAdornment={
                        <CalendarIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }
                    >
                      {getAvailableDates().map((date) => (
                        <MenuItem key={date} value={date}>
                          {new Date(date).toLocaleDateString('en-US', { 
                            weekday: 'long', 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                          })}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  
                  <FormControl fullWidth sx={{ mb: 3 }} disabled={!selectedDate}>
                    <InputLabel id="time-select-label">Time</InputLabel>
                    <Select
                      labelId="time-select-label"
                      id="time-select"
                      value={selectedTime}
                      label="Time"
                      onChange={handleTimeChange}
                      startAdornment={
                        <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }
                    >
                      {availableTimes.map((time) => (
                        <MenuItem key={time} value={time}>
                          {time}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  
                  <TextField
                    fullWidth
                    id="appointment-reason"
                    label="Reason for Visit"
                    multiline
                    rows={4}
                    value={appointmentReason}
                    onChange={handleReasonChange}
                    sx={{ mb: 3 }}
                  />
                  
                  <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    fullWidth
                    onClick={handleBookAppointment}
                    disabled={!selectedDoctor || !selectedDate || !selectedTime || !appointmentReason}
                  >
                    Book Appointment
                  </Button>
                </Box>
              )}
            </Paper>
          </Grid>
          
          {/* Upcoming Appointments */}
          <Grid item xs={12} md={5}>
            <Paper sx={{ p: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom>
                Your Upcoming Appointments
              </Typography>
              
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                  <CircularProgress />
                </Box>
              ) : upcomingAppointments.length > 0 ? (
                <Box>
                  {upcomingAppointments.map((appointment) => (
                    <Card key={appointment.id} variant="outlined" sx={{ mb: 2 }}>
                      <CardContent>
                        <Typography variant="h6" component="div">
                          {appointment.doctor}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <CalendarIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {appointment.date}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <TimeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {appointment.time}
                          </Typography>
                        </Box>
                        <Divider sx={{ my: 1 }} />
                        <Typography variant="body2">
                          {appointment.reason}
                        </Typography>
                      </CardContent>
                      <CardActions>
                        <Button size="small" color="primary">
                          Reschedule
                        </Button>
                        <Button size="small" color="error">
                          Cancel
                        </Button>
                      </CardActions>
                    </Card>
                  ))}
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body1" color="text.secondary">
                    You have no upcoming appointments.
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Container>
      
      {/* Confirmation Dialog */}
      <Dialog open={openDialog} onClose={handleCancelDialog}>
        <DialogTitle>Confirm Appointment</DialogTitle>
        <DialogContent>
          {bookingSuccess ? (
            <Alert severity="success" sx={{ mt: 2 }}>
              Appointment booked successfully!
            </Alert>
          ) : loading ? (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body1" gutterBottom>
                Processing your appointment...
              </Typography>
              <LinearProgress />
            </Box>
          ) : (
            <>
              <Typography variant="body1" gutterBottom sx={{ mt: 2 }}>
                Please confirm your appointment details:
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Doctor:</strong> {doctors.find(d => d.id === selectedDoctor)?.name}
                </Typography>
                <Typography variant="body2">
                  <strong>Date:</strong> {selectedDate}
                </Typography>
                <Typography variant="body2">
                  <strong>Time:</strong> {selectedTime}
                </Typography>
                <Typography variant="body2">
                  <strong>Reason:</strong> {appointmentReason}
                </Typography>
              </Box>
            </>
          )}
        </DialogContent>
        {!bookingSuccess && !loading && (
          <DialogActions>
            <Button onClick={handleCancelDialog}>Cancel</Button>
            <Button onClick={handleConfirmBooking} variant="contained" color="primary">
              Confirm Booking
            </Button>
          </DialogActions>
        )}
      </Dialog>
    </Layout>
  );
};

export default AppointmentBookingPage;
