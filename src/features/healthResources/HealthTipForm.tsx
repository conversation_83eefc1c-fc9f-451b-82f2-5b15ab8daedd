import React, { useState } from 'react';
import { useSupabaseFileUpload } from '../../hooks/useSupabaseFileUpload';
import { createHealthTip } from './healthTipsService';
import { useAuth } from '../../contexts/AuthContext';

interface HealthTipFormProps {
  userId: string;
  onSubmit: (tip: { title: string; content: string; mediaUrl?: string }) => void;
}

const HealthTipForm: React.FC<HealthTipFormProps> = ({ userId, onSubmit }) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [file, setFile] = useState<File | null>(null);
  const { uploadState, uploadMedia, resetUploadState } = useSupabaseFileUpload();
  const { currentUser } = useAuth();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
      resetUploadState();
    }
  };

  const handleUpload = async () => {
    if (file) {
      await uploadMedia(userId, file, 'HEALTH_TIPS_MEDIA');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    let mediaUrl = uploadState.fileUrl;
    if (file && !mediaUrl) {
      await handleUpload();
      mediaUrl = uploadState.fileUrl;
    }
    // Save to Firestore
    if (currentUser) {
      await createHealthTip({
        title,
        content,
        mediaUrl: mediaUrl || undefined,
        authorId: currentUser.uid,
        published: true
      });
    }
    onSubmit({ title, content, mediaUrl: mediaUrl || undefined });
    setTitle('');
    setContent('');
    setFile(null);
    resetUploadState();
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="Title"
        value={title}
        onChange={e => setTitle(e.target.value)}
        required
      />
      <textarea
        placeholder="Content"
        value={content}
        onChange={e => setContent(e.target.value)}
        required
      />
      <input type="file" accept="image/*,video/*" onChange={handleFileChange} />
      {uploadState.isUploading && <div>Uploading... {uploadState.progress}%</div>}
      {uploadState.error && <div style={{ color: 'red' }}>{uploadState.error}</div>}
      <button type="submit" disabled={uploadState.isUploading}>Create Health Tip</button>
    </form>
  );
};

export default HealthTipForm;
