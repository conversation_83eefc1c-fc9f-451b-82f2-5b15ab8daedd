import { useEffect, useState } from 'react';
import { getHealthTips } from './healthTipsService';

export const useHealthTips = () => {
  const [tips, setTips] = useState<any[]>([]); // Fix: use any[] to avoid TS 'never' errors
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;
    setLoading(true);
    getHealthTips()
      .then((data) => {
        if (mounted) {
          setTips(data.filter(tip => tip.published));
          setLoading(false);
        }
      })
      .catch((err) => {
        if (mounted) {
          setError('Failed to load health tips');
          setLoading(false);
        }
      });
    return () => {
      mounted = false;
    };
  }, []);

  return { tips, loading, error };
};
