import React from 'react';
import { useHealthTips } from './useHealthTips';

const HealthTipsFeed: React.FC = () => {
  const { tips, loading, error } = useHealthTips();

  if (loading) return <div>Loading health tips...</div>;
  if (error) return <div style={{ color: 'red' }}>{error}</div>;
  if (!tips.length) return <div>No health tips available.</div>;

  return (
    <div className="space-y-6">
      {tips.map(tip => (
        <div key={tip.id} className="border rounded p-4 bg-white shadow">
          <h3 className="text-lg font-bold mb-2">{tip.title}</h3>
          <div className="mb-2">{tip.content}</div>
          {tip.mediaUrl && (
            tip.mediaUrl.match(/\.(jpg|jpeg|png|gif)$/i) ? (
              <img src={tip.mediaUrl} alt="Health Tip Media" className="max-w-xs rounded" />
            ) : (
              <video src={tip.mediaUrl} controls className="max-w-xs rounded" />
            )
          )}
          <div className="text-xs text-gray-500 mt-2">By {tip.authorId}</div>
        </div>
      ))}
    </div>
  );
};

export default HealthTipsFeed;
