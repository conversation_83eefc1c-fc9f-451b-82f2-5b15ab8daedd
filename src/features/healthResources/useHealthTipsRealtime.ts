import { useEffect, useState } from 'react';
import { onSnapshot, collection, query, where } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { HealthTip } from './healthTipsService';

export const useHealthTipsRealtime = () => {
  const [tips, setTips] = useState<HealthTip[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    const q = query(collection(db, 'healthTips'), where('published', '==', true));
    const unsubscribe = onSnapshot(q, (snapshot) => {
      setTips(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as HealthTip)));
      setLoading(false);
    }, (err) => {
      setError('Failed to load health tips');
      setLoading(false);
    });
    return () => unsubscribe();
  }, []);

  return { tips, loading, error };
};
