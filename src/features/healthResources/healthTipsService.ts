import { addDocument, queryDocuments } from '../../services/firestoreService';

export interface HealthTip {
  id?: string;
  title: string;
  content: string;
  mediaUrl?: string;
  authorId: string;
  published: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

const COLLECTION = 'healthTips';

export const createHealthTip = async (tip: Omit<HealthTip, 'id' | 'createdAt' | 'updatedAt'>) => {
  return addDocument<HealthTip>(COLLECTION, tip);
};

export const getHealthTips = async () => {
  return queryDocuments<HealthTip>(COLLECTION, [], true);
};
