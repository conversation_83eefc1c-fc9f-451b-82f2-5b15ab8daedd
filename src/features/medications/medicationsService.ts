import { addDocument, queryDocuments, deleteDocument, updateDocument } from '../../services/firestoreService';

export interface Medication {
  id?: string;
  userId: string;
  name: string;
  dosage: string;
  frequency: string;
  timeOfDay: string[];
  startDate: string;
  endDate: string;
  instructions: string;
  remainingPills: number;
  refillReminder: boolean;
  status: 'active' | 'completed' | 'paused';
  createdAt?: Date;
  updatedAt?: Date;
}

const COLLECTION = 'medications';

export const createMedication = async (med: Omit<Medication, 'id' | 'createdAt' | 'updatedAt'>) => {
  return addDocument<Medication>(COLLECTION, med);
};

export const getMedications = async (userId: string) => {
  return queryDocuments<Medication>(COLLECTION, [], true).then(meds =>
    meds.filter(m => m.userId === userId)
  );
};

export const deleteMedication = async (id: string) => {
  return deleteDocument(COLLECTION, id);
};

export const updateMedication = async (id: string, med: Medication) => {
  return updateDocument(COLLECTION, id, med);
};
