// DoctorProfileForm.tsx
// Multi-step form for collecting comprehensive doctor profile information

import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  CircularProgress,
  Paper
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '../services/firebase';
import type { UserProfile } from '../types/firebase';

interface DoctorProfileFormProps {
  open: boolean;
  onClose: () => void;
  initialStep?: number;
  isOverlay?: boolean;
}

const steps = ['Personal Details', 'Professional Info', 'Preferences'];
const specialties = ['Internal Medicine', 'General Practice', 'Pediatrics', 'Dermatology', 'Psychiatry', 'Other'];
const departments = ['Student Health Services', 'Primary Care', 'Emergency Care', 'Counseling Services', 'Other'];
const genders = ['Male', 'Female', 'Other', 'Prefer not to say'];

const DoctorProfileForm: React.FC<DoctorProfileFormProps> = ({
  open,
  onClose,
  initialStep = 0,
  isOverlay = false
}) => {
  const { userProfile, currentUser } = useAuth();
  const [activeStep, setActiveStep] = useState(initialStep);
  const [formData, setFormData] = useState({
    firstName: userProfile?.firstName || '',
    lastName: userProfile?.lastName || '',
    email: userProfile?.email || '',
    phone: userProfile?.phone || '',
    gender: userProfile?.gender || '',
    dateOfBirth: userProfile?.dateOfBirth || '',
    specialty: userProfile?.specialty || '',
    department: userProfile?.department || '',
    licenseNumber: userProfile?.licenseNumber || '',
    yearsOfExperience: userProfile?.yearsOfExperience || '',
    education: userProfile?.education || '',
    certifications: userProfile?.certifications || '',
    preferences: userProfile?.preferences || {}
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNext = () => {
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError('');
    try {
      if (!currentUser) throw new Error('No user');
      await updateDoc(doc(db, 'users', currentUser.uid), {
        ...formData,
        profileCompleted: true,
        updatedAt: new Date()
      });
      onClose();
    } catch (err: any) {
      setError(err.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={isOverlay ? undefined : onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Complete Your Doctor Profile</DialogTitle>
      <DialogContent>
        <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 2 }}>
          {steps.map(label => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        {activeStep === 0 && (
          <Box>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <TextField label="First Name" fullWidth value={formData.firstName} onChange={e => handleChange('firstName', e.target.value)} />
              </Grid>
              <Grid item xs={6}>
                <TextField label="Last Name" fullWidth value={formData.lastName} onChange={e => handleChange('lastName', e.target.value)} />
              </Grid>
              <Grid item xs={12}>
                <TextField label="Email" fullWidth value={formData.email} disabled />
              </Grid>
              <Grid item xs={6}>
                <TextField label="Phone" fullWidth value={formData.phone} onChange={e => handleChange('phone', e.target.value)} />
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <InputLabel>Gender</InputLabel>
                  <Select value={formData.gender} label="Gender" onChange={e => handleChange('gender', e.target.value)}>
                    {genders.map(g => <MenuItem key={g} value={g}>{g}</MenuItem>)}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField label="Date of Birth" type="date" fullWidth InputLabelProps={{ shrink: true }} value={formData.dateOfBirth} onChange={e => handleChange('dateOfBirth', e.target.value)} />
              </Grid>
            </Grid>
          </Box>
        )}
        {activeStep === 1 && (
          <Box>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <InputLabel>Specialty</InputLabel>
                  <Select value={formData.specialty} label="Specialty" onChange={e => handleChange('specialty', e.target.value)}>
                    {specialties.map(s => <MenuItem key={s} value={s}>{s}</MenuItem>)}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <InputLabel>Department</InputLabel>
                  <Select value={formData.department} label="Department" onChange={e => handleChange('department', e.target.value)}>
                    {departments.map(d => <MenuItem key={d} value={d}>{d}</MenuItem>)}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <TextField label="License Number" fullWidth value={formData.licenseNumber} onChange={e => handleChange('licenseNumber', e.target.value)} />
              </Grid>
              <Grid item xs={6}>
                <TextField label="Years of Experience" type="number" fullWidth value={formData.yearsOfExperience} onChange={e => handleChange('yearsOfExperience', e.target.value)} />
              </Grid>
              <Grid item xs={12}>
                <TextField label="Education" fullWidth value={formData.education} onChange={e => handleChange('education', e.target.value)} />
              </Grid>
              <Grid item xs={12}>
                <TextField label="Certifications (comma separated)" fullWidth value={formData.certifications} onChange={e => handleChange('certifications', e.target.value)} />
              </Grid>
            </Grid>
          </Box>
        )}
        {activeStep === 2 && (
          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField label="Preferences (JSON)" fullWidth value={JSON.stringify(formData.preferences)} onChange={e => handleChange('preferences', e.target.value)} />
              </Grid>
            </Grid>
          </Box>
        )}
        {error && <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>}
      </DialogContent>
      <DialogActions>
        {activeStep > 0 && <Button onClick={handleBack}>Back</Button>}
        {activeStep < steps.length - 1 && <Button onClick={handleNext}>Next</Button>}
        {activeStep === steps.length - 1 && <Button onClick={handleSubmit} disabled={loading}>{loading ? <CircularProgress size={20} /> : 'Finish'}</Button>}
      </DialogActions>
    </Dialog>
  );
};

export default DoctorProfileForm;
