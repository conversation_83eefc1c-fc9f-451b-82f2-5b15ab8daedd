import { Button, ButtonProps, CircularProgress } from '@mui/material';

interface LoadingButtonProps extends ButtonProps {
  loading?: boolean;
  loadingPosition?: 'start' | 'end' | 'center';
}

const LoadingButton = ({ 
  children, 
  loading = false, 
  loadingPosition = 'center',
  disabled,
  ...props 
}: LoadingButtonProps) => {
  
  const getLoadingStyles = () => {
    if (loadingPosition === 'start') {
      return { marginRight: 1 };
    } else if (loadingPosition === 'end') {
      return { marginLeft: 1 };
    }
    return {};
  };

  const renderContent = () => {
    if (!loading) return children;

    if (loadingPosition === 'center') {
      return <CircularProgress size={24} color="inherit" />;
    }

    return (
      <>
        {loadingPosition === 'start' && (
          <CircularProgress size={20} color="inherit" sx={getLoadingStyles()} />
        )}
        {children}
        {loadingPosition === 'end' && (
          <CircularProgress size={20} color="inherit" sx={getLoadingStyles()} />
        )}
      </>
    );
  };

  return (
    <Button
      disabled={loading || disabled}
      {...props}
    >
      {renderContent()}
    </Button>
  );
};

export default LoadingButton;