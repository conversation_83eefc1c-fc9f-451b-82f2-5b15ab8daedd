import { Box, Typography, TypographyProps } from '@mui/material';

interface SectionHeadingProps {
  title: string;
  subtitle?: string;
  align?: 'left' | 'center' | 'right';
  titleProps?: TypographyProps;
  subtitleProps?: TypographyProps;
}

const SectionHeading = ({ 
  title, 
  subtitle, 
  align = 'center',
  titleProps,
  subtitleProps
}: SectionHeadingProps) => {
  return (
    <Box sx={{ textAlign: align, mb: 6 }}>
      <Typography 
        variant="h3" 
        component="h2" 
        fontWeight="bold" 
        gutterBottom
        {...titleProps}
      >
        {title}
      </Typography>
      {subtitle && (
        <Typography 
          variant="h6" 
          color="text.secondary" 
          sx={{ maxWidth: '700px', mx: align === 'center' ? 'auto' : 0 }}
          {...subtitleProps}
        >
          {subtitle}
        </Typography>
      )}
    </Box>
  );
};

export default SectionHeading;