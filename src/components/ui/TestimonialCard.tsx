import { Box, Card, CardContent, Typography, Avatar, CardProps } from '@mui/material';

interface TestimonialCardProps extends CardProps {
  name: string;
  role: string;
  quote: string;
  image?: string;
}

const TestimonialCard = ({ 
  name, 
  role, 
  quote, 
  image,
  ...props 
}: TestimonialCardProps) => {
  return (
    <Card 
      sx={{ 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        '&:hover': {
          transform: 'translateY(-8px)',
          boxShadow: '0 20px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04)'
        },
        ...props.sx
      }}
      {...props}
    >
      <CardContent sx={{ flexGrow: 1, p: 4 }}>
        <Typography variant="body1" sx={{ mb: 3, fontStyle: 'italic' }}>
          "{quote}"
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {image ? (
            <Avatar
              src={image}
              alt={name}
              sx={{ 
                width: 50, 
                height: 50,
                mr: 2
              }}
            />
          ) : (
            <Box 
              sx={{ 
                width: 50, 
                height: 50, 
                borderRadius: '50%', 
                bgcolor: 'primary.light',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                mr: 2
              }}
            >
              {name.charAt(0)}
            </Box>
          )}
          <Box>
            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
              {name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {role}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default TestimonialCard;
