import React, { useEffect, useRef } from 'react';
import { Box, BoxProps } from '@mui/material';

interface AnimatedSectionProps extends BoxProps {
  children: React.ReactNode;
  delay?: number;
}

const AnimatedSection: React.FC<AnimatedSectionProps> = ({ 
  children, 
  delay = 0, 
  ...props 
}) => {
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const section = sectionRef.current;
    if (!section) return;

    // Simple animation without framer-motion
    section.style.opacity = '0';
    section.style.transform = 'translateY(20px)';
    section.style.transition = `opacity 0.6s ease, transform 0.6s ease`;
    
    const timeoutId = setTimeout(() => {
      section.style.opacity = '1';
      section.style.transform = 'translateY(0)';
    }, delay * 1000);

    return () => clearTimeout(timeoutId);
  }, [delay]);

  return (
    <Box
      ref={sectionRef}
      {...props}
    >
      {children}
    </Box>
  );
};

export default AnimatedSection;


