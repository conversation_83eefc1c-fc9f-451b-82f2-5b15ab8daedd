// DoctorProfileCompletionOverlay.tsx
// Overlay to force doctors to complete their profile on first login

import React, { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, Box, Stepper, Step, StepLabel, <PERSON>ton, Alert, LinearProgress } from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import DoctorProfileForm from './DoctorProfileForm';

interface DoctorProfileCompletionOverlayProps {
  open: boolean;
  onComplete: () => void;
}

const steps = [
  'Personal Details',
  'Professional Info',
  'Preferences'
];

const DoctorProfileCompletionOverlay: React.FC<DoctorProfileCompletionOverlayProps> = ({ open, onComplete }) => {
  const { userProfile } = useAuth();
  const [showForm, setShowForm] = useState(true);

  useEffect(() => {
    // If profile is already completed, close overlay
    if (userProfile && userProfile.profileCompleted) {
      setShowForm(false);
      onComplete();
    }
  }, [userProfile, onComplete]);

  if (!showForm) return null;

  return (
    <Dialog open={open} disableEscapeKeyDown fullWidth maxWidth="sm">
      <DialogTitle>Complete Your Profile</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Stepper activeStep={0} alternativeLabel>
            {steps.map(label => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>
        <Alert severity="info" sx={{ mb: 2 }}>
          Please complete your profile to access the doctor dashboard.
        </Alert>
        <DoctorProfileForm open={true} onClose={onComplete} isOverlay={true} />
        <LinearProgress sx={{ mt: 2 }} />
      </DialogContent>
    </Dialog>
  );
};

export default DoctorProfileCompletionOverlay;
