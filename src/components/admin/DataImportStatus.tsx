import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Alert,
  CircularProgress,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  Upload as UploadIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  CloudUpload as CloudIcon
} from '@mui/icons-material';
import { 
  getAllSymptoms, 
  getAllConditions,
  importSymptomsFromMock,
  importConditionsFromMock
} from '../../services/symptomConditionService';
import { symptoms as mockSymptoms, conditions as mockConditions } from '../../services/mockSymptomCheckerService';

interface DataStatus {
  symptomsCount: number;
  conditionsCount: number;
  isLoading: boolean;
  hasData: boolean;
}

const DataImportStatus: React.FC = () => {
  const [status, setStatus] = useState<DataStatus>({
    symptomsCount: 0,
    conditionsCount: 0,
    isLoading: true,
    hasData: false
  });
  const [importing, setImporting] = useState(false);
  const [importSuccess, setImportSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check current data status
  const checkDataStatus = async () => {
    try {
      setStatus(prev => ({ ...prev, isLoading: true }));
      
      const [symptoms, conditions] = await Promise.all([
        getAllSymptoms(),
        getAllConditions()
      ]);

      setStatus({
        symptomsCount: symptoms.length,
        conditionsCount: conditions.length,
        isLoading: false,
        hasData: symptoms.length > 0 && conditions.length > 0
      });
    } catch (err) {
      console.error('Error checking data status:', err);
      setStatus(prev => ({ ...prev, isLoading: false }));
    }
  };

  // Import mock data to Firebase
  const handleImportData = async () => {
    try {
      setImporting(true);
      setError(null);
      setImportSuccess(false);

      console.log('🚀 Starting import of mock data...');
      
      await Promise.all([
        importSymptomsFromMock(mockSymptoms),
        importConditionsFromMock(mockConditions)
      ]);

      console.log('✅ Import completed successfully!');
      setImportSuccess(true);
      
      // Refresh status
      await checkDataStatus();
      
    } catch (err: any) {
      console.error('❌ Import failed:', err);
      setError(err.message || 'Failed to import data');
    } finally {
      setImporting(false);
    }
  };

  useEffect(() => {
    checkDataStatus();
  }, []);

  if (status.isLoading) {
    return (
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <CircularProgress size={24} />
            <Typography>Checking symptom checker data status...</Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <CloudIcon color="primary" />
          <Typography variant="h6" fontWeight="bold">
            Symptom Checker Database Status
          </Typography>
        </Box>

        {/* Current Status */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <Chip
              icon={status.symptomsCount > 0 ? <CheckIcon /> : <WarningIcon />}
              label={`${status.symptomsCount} Symptoms`}
              color={status.symptomsCount > 0 ? 'success' : 'warning'}
              variant="outlined"
            />
            <Chip
              icon={status.conditionsCount > 0 ? <CheckIcon /> : <WarningIcon />}
              label={`${status.conditionsCount} Conditions`}
              color={status.conditionsCount > 0 ? 'success' : 'warning'}
              variant="outlined"
            />
          </Box>

          {status.hasData ? (
            <Alert severity="success" sx={{ mb: 2 }}>
              <Typography variant="body2" fontWeight="medium">
                ✅ Symptom Checker Database is Live!
              </Typography>
              <Typography variant="body2">
                Your symptom checker is using live Firebase data with {status.symptomsCount} symptoms and {status.conditionsCount} conditions.
              </Typography>
            </Alert>
          ) : (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2" fontWeight="medium">
                ⚠️ No Data Found
              </Typography>
              <Typography variant="body2">
                The symptom checker database is empty. Import mock data to get started with 220+ symptoms and 500+ conditions.
              </Typography>
            </Alert>
          )}
        </Box>

        {/* Import Section */}
        {!status.hasData && (
          <Box>
            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              Import Mock Data
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Import comprehensive medical data including {mockSymptoms.length} symptoms and {mockConditions.length} conditions.
            </Typography>

            {importing && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  Importing data to Firebase...
                </Typography>
                <LinearProgress />
              </Box>
            )}

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {importSuccess && (
              <Alert severity="success" sx={{ mb: 2 }}>
                Data imported successfully! The symptom checker is now using live Firebase data.
              </Alert>
            )}

            <Button
              variant="contained"
              startIcon={<UploadIcon />}
              onClick={handleImportData}
              disabled={importing}
              sx={{ mr: 2 }}
            >
              {importing ? 'Importing...' : 'Import Mock Data'}
            </Button>

            <Button
              variant="outlined"
              onClick={checkDataStatus}
              disabled={importing}
            >
              Refresh Status
            </Button>
          </Box>
        )}

        {/* Refresh Button for existing data */}
        {status.hasData && (
          <Box sx={{ mt: 2 }}>
            <Button
              variant="outlined"
              onClick={checkDataStatus}
              size="small"
            >
              Refresh Status
            </Button>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default DataImportStatus;
