import React from 'react';
import { Box, Container, Typography, Grid, <PERSON>, Divider } from '@mui/material';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';

const Footer = () => {
  return (
    <Box component="footer" sx={{ bgcolor: 'primary.dark', color: 'white', py: 6, mt: 'auto' }}>
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <LocalHospitalIcon sx={{ mr: 1 }} />
              <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
                University Health
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ mb: 2, opacity: 0.8 }}>
              Providing quality healthcare services to our university community.
            </Typography>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
              Quick Links
            </Typography>
            <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
              <Box component="li" sx={{ mb: 1 }}>
                <Link href="/" color="inherit" underline="hover">Home</Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link href="/#about" color="inherit" underline="hover">About</Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link href="/#services" color="inherit" underline="hover">Services</Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link href="/#contact" color="inherit" underline="hover">Contact</Link>
              </Box>
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
              Contact
            </Typography>
            <Typography variant="body2" sx={{ mb: 1, opacity: 0.8 }}>
              123 University Ave, Campus Building
            </Typography>
            <Typography variant="body2" sx={{ mb: 1, opacity: 0.8 }}>
              Email: <EMAIL>
            </Typography>
            <Typography variant="body2" sx={{ mb: 1, opacity: 0.8 }}>
              Phone: (*************
            </Typography>
          </Grid>
        </Grid>
        
        <Divider sx={{ my: 4, borderColor: 'rgba(255,255,255,0.1)' }} />
        
        <Typography variant="body2" align="center" sx={{ opacity: 0.8 }}>
          © {new Date().getFullYear()} Esther Elaigwu. All rights reserved.
        </Typography>
      </Container>
    </Box>
  );
};

export default Footer;