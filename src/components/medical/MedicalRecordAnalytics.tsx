import React from 'react';
import { 
  Box, 
  Paper, 
  Typography, 
  Grid,
  Divider
} from '@mui/material';
import { 
  BarChart, 
  <PERSON>, 
  XAxis, 
  <PERSON>A<PERSON>s, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  LineChart,
  Line
} from 'recharts';

interface MedicalRecordAnalyticsProps {
  monthlyRecords: { month: string; count: number }[];
  diagnosisDistribution: { name: string; value: number }[];
  doctorWorkload: { name: string; records: number }[];
}

const MedicalRecordAnalytics: React.FC<MedicalRecordAnalyticsProps> = ({
  monthlyRecords,
  diagnosisDistribution,
  doctorWorkload
}) => {
  // Colors for pie chart
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];
  
  return (
    <Grid container spacing={3}>
      {/* Monthly Records Trend */}
      <Grid item xs={12} lg={8}>
        <Paper 
          sx={{ 
            p: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            height: '100%'
          }}
        >
          <Typography variant="h6" fontWeight="medium" gutterBottom>
            Monthly Medical Records
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <Box sx={{ height: 300 }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={monthlyRecords}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" name="Number of Records" fill="#3f51b5" />
              </BarChart>
            </ResponsiveContainer>
          </Box>
        </Paper>
      </Grid>
      
      {/* Diagnosis Distribution */}
      <Grid item xs={12} md={6} lg={4}>
        <Paper 
          sx={{ 
            p: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            height: '100%'
          }}
        >
          <Typography variant="h6" fontWeight="medium" gutterBottom>
            Diagnosis Distribution
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <Box sx={{ height: 300, display: 'flex', justifyContent: 'center' }}>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={diagnosisDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {diagnosisDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Box>
        </Paper>
      </Grid>
      
      {/* Doctor Workload */}
      <Grid item xs={12}>
        <Paper 
          sx={{ 
            p: 3, 
            borderRadius: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Typography variant="h6" fontWeight="medium" gutterBottom>
            Doctor Workload
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <Box sx={{ height: 300 }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={doctorWorkload}
                layout="vertical"
                margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis type="category" dataKey="name" />
                <Tooltip />
                <Legend />
                <Bar dataKey="records" name="Number of Records" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </Box>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default MedicalRecordAnalytics;