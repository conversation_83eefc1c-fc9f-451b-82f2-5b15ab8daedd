
// User types
export interface FirebaseUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
}

// Auth credential types
export interface UserCredential {
  user: FirebaseUser;
}

export interface AuthCredential {
  providerId: string;
  signInMethod: string;
}

// User roles as both type and value
export const UserRoles = {
  STUDENT: 'student',
  DOCTOR: 'doctor',
  ADMIN: 'admin'
} as const;

export type UserRole = typeof UserRoles[keyof typeof UserRoles];

// Emergency contact information
export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
  address?: string;
}

// Medical information
export interface MedicalInformation {
  bloodType?: string;
  allergies: string[];
  medications: string[];
  conditions: string[];
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  primaryPhysician?: string;
  primaryPhysicianPhone?: string;
  medicalNotes?: string;
}

// Profile completion status
export interface ProfileCompletionStatus {
  isComplete: boolean;
  personalDetailsComplete: boolean;
  medicalInfoComplete: boolean;
  emergencyContactComplete: boolean;
  lastUpdated: Date;
}

// User profile data
export interface UserProfile {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  role: UserRole;
  phoneNumber?: string;
  department?: string;
  specialty?: string; // For doctors
  studentId?: string; // For students

  // Extended student profile fields
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  year?: string; // Academic year (Freshman, Sophomore, etc.)
  major?: string;
  status?: string; // active, inactive, graduated, etc.

  // Medical information
  medicalInfo?: MedicalInformation;

  // Emergency contact
  emergencyContact?: EmergencyContact;

  // Profile completion tracking
  profileCompletion?: ProfileCompletionStatus;

  createdAt: Date;
  updatedAt: Date;
}


