// Medical Data Types
// Types for health metrics, medications, medical records, and health tips

export interface HealthMetric {
  id: string;
  studentId: string;
  doctorId: string;
  type: 'vitals' | 'lab_results' | 'measurements' | 'other';
  category: string; // e.g., 'Blood Pressure', 'Blood Test', 'Weight'
  value: string | number;
  unit?: string;
  normalRange?: {
    min: number;
    max: number;
  };
  status: 'normal' | 'abnormal' | 'critical' | 'pending';
  notes?: string;
  attachments?: FileAttachment[];
  recordedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Medication {
  id: string;
  studentId: string;
  doctorId: string;
  name: string;
  dosage: string;
  frequency: string; // e.g., 'Once daily', 'Twice daily', 'As needed'
  route: string; // e.g., 'Oral', 'Topical', 'Injection'
  startDate: Date;
  endDate?: Date;
  isActive: boolean;
  instructions: string;
  sideEffects?: string[];
  interactions?: string[];
  refillsRemaining?: number;
  lastRefillDate?: Date;
  nextRefillDue?: Date;
  prescriptionImage?: string; // Supabase file path
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface MedicalRecord {
  id: string;
  studentId: string;
  doctorId?: string; // Can be uploaded by student or doctor
  uploadedBy: 'student' | 'doctor';
  title: string;
  description?: string;
  category: 'diagnosis' | 'treatment' | 'lab_results' | 'imaging' | 'prescription' | 'other';
  date: Date; // Date of the medical event
  files: FileAttachment[];
  tags?: string[];
  isPrivate: boolean;
  sharedWith?: string[]; // Array of doctor IDs who can access
  createdAt: Date;
  updatedAt: Date;
}

export interface FileAttachment {
  id: string;
  fileName: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  supabasePath: string;
  publicUrl?: string;
  signedUrl?: string;
  uploadedBy: string; // User ID
  uploadedAt: Date;
}

export interface HealthTip {
  id: string;
  doctorId: string;
  doctorName: string;
  title: string;
  content: string; // Rich text content
  summary: string; // Short description for cards
  category: 'mental_health' | 'nutrition' | 'exercise' | 'wellness' | 'safety' | 'general';
  tags: string[];
  featuredImage?: string; // Supabase file path
  attachments?: FileAttachment[];
  isPublished: boolean;
  publishedAt?: Date;
  views: number;
  likes: number;
  targetAudience?: 'all' | 'specific'; // Can target specific students
  targetStudents?: string[]; // Array of student IDs
  createdAt: Date;
  updatedAt: Date;
}

export interface Notification {
  id: string;
  userId: string; // Recipient
  type: 'appointment' | 'medication' | 'health_tip' | 'medical_record' | 'general';
  title: string;
  message: string;
  data?: any; // Additional data for the notification
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  scheduledFor?: Date; // For scheduled notifications
  expiresAt?: Date;
  createdAt: Date;
  readAt?: Date;
}

export interface StudentHealthSummary {
  studentId: string;
  studentName: string;
  studentEmail: string;
  lastVisit?: Date;
  upcomingAppointments: number;
  activemedications: number;
  recentMetrics: HealthMetric[];
  criticalAlerts: number;
  profileCompletion: number;
  lastActivity: Date;
}

export interface DoctorStudentAssignment {
  doctorId: string;
  studentId: string;
  assignedAt: Date;
  isActive: boolean;
  specialtyFocus?: string;
  notes?: string;
}

// Form types for creating/editing
export interface CreateHealthMetricForm {
  studentId: string;
  type: HealthMetric['type'];
  category: string;
  value: string | number;
  unit?: string;
  normalRange?: {
    min: number;
    max: number;
  };
  status: HealthMetric['status'];
  notes?: string;
  recordedAt: Date;
}

export interface CreateMedicationForm {
  studentId: string;
  name: string;
  dosage: string;
  frequency: string;
  route: string;
  startDate: Date;
  endDate?: Date;
  instructions: string;
  sideEffects?: string[];
  interactions?: string[];
  refillsRemaining?: number;
  notes?: string;
}

export interface CreateMedicalRecordForm {
  studentId: string;
  title: string;
  description?: string;
  category: MedicalRecord['category'];
  date: Date;
  files: File[];
  tags?: string[];
  isPrivate: boolean;
  sharedWith?: string[];
}

export interface CreateHealthTipForm {
  title: string;
  content: string;
  summary: string;
  category: HealthTip['category'];
  tags: string[];
  featuredImage?: File;
  attachments?: File[];
  targetAudience: HealthTip['targetAudience'];
  targetStudents?: string[];
}

// Filter and search types
export interface HealthMetricFilters {
  studentId?: string;
  type?: HealthMetric['type'];
  category?: string;
  status?: HealthMetric['status'];
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface MedicationFilters {
  studentId?: string;
  isActive?: boolean;
  needsRefill?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface MedicalRecordFilters {
  studentId?: string;
  category?: MedicalRecord['category'];
  uploadedBy?: 'student' | 'doctor';
  dateRange?: {
    start: Date;
    end: Date;
  };
  tags?: string[];
}

// Statistics and analytics types
export interface HealthMetricsStats {
  totalMetrics: number;
  criticalCount: number;
  abnormalCount: number;
  normalCount: number;
  pendingCount: number;
  recentTrends: {
    category: string;
    trend: 'improving' | 'stable' | 'declining';
    change: number;
  }[];
}

export interface MedicationStats {
  totalActive: number;
  totalInactive: number;
  needingRefill: number;
  expiringSoon: number;
  commonMedications: {
    name: string;
    count: number;
  }[];
}

export interface StudentOverviewStats {
  totalStudents: number;
  activeStudents: number;
  studentsWithCriticalAlerts: number;
  averageProfileCompletion: number;
  recentActivity: {
    date: Date;
    count: number;
  }[];
}
