import type { Timestamp } from 'firebase/firestore';

// Message types
export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderRole: 'user' | 'doctor';
  senderName: string;
  senderAvatar?: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'system';
  timestamp: Timestamp;
  readBy: string[]; // Array of user IDs who have read this message
  edited?: boolean;
  editedAt?: Timestamp;
  replyTo?: string; // Message ID this is replying to
}

// Conversation types
export interface Conversation {
  id: string;
  participants: string[]; // Array of user IDs (user + doctor)
  participantDetails: {
    [userId: string]: {
      name: string;
      role: 'user' | 'doctor';
      avatar?: string;
      specialty?: string; // For doctors
    };
  };
  lastMessage?: {
    content: string;
    senderId: string;
    timestamp: Timestamp;
    type: string;
  };
  lastActivity: Timestamp;
  unreadCounts: {
    [userId: string]: number;
  };
  status: 'active' | 'archived' | 'blocked';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Call types
export interface CallSession {
  id: string;
  conversationId: string;
  callerId: string;
  calleeId: string;
  type: 'video' | 'voice';
  status: 'initiating' | 'ringing' | 'active' | 'ended' | 'declined' | 'missed';
  startedAt?: Timestamp;
  endedAt?: Timestamp;
  duration?: number; // in seconds
  offer?: RTCSessionDescriptionInit;
  answer?: RTCSessionDescriptionInit;
  iceCandidates: {
    [userId: string]: RTCIceCandidateInit[];
  };
}

// Presence types
export interface UserPresence {
  userId: string;
  status: 'online' | 'offline' | 'away' | 'busy';
  lastSeen: Timestamp;
  currentCall?: string; // Call session ID if in a call
}

// Typing indicator
export interface TypingIndicator {
  conversationId: string;
  userId: string;
  userName: string;
  timestamp: Timestamp;
}

// Notification types
export interface ChatNotification {
  id: string;
  userId: string;
  type: 'new_message' | 'incoming_call' | 'missed_call' | 'call_ended';
  title: string;
  body: string;
  data: {
    conversationId?: string;
    callId?: string;
    senderId?: string;
  };
  read: boolean;
  createdAt: Timestamp;
}

// Service interfaces
export interface ChatService {
  // Conversations
  createConversation(userId: string, doctorId: string): Promise<string>;
  getConversations(userId: string): Promise<Conversation[]>;
  subscribeToConversations(userId: string, callback: (conversations: Conversation[]) => void): () => void;
  
  // Messages
  sendMessage(conversationId: string, senderId: string, content: string, type?: string): Promise<string>;
  getMessages(conversationId: string, limit?: number): Promise<Message[]>;
  subscribeToMessages(conversationId: string, callback: (messages: Message[]) => void): () => void;
  markMessagesAsRead(conversationId: string, userId: string): Promise<void>;
  
  // Typing indicators
  setTyping(conversationId: string, userId: string, userName: string): Promise<void>;
  subscribeToTyping(conversationId: string, callback: (typing: TypingIndicator[]) => void): () => void;
  
  // Presence
  setUserPresence(userId: string, status: 'online' | 'offline' | 'away' | 'busy'): Promise<void>;
  subscribeToPresence(userIds: string[], callback: (presence: UserPresence[]) => void): () => void;
}

export interface CallService {
  // Call management
  initiateCall(conversationId: string, callerId: string, calleeId: string, type: 'video' | 'voice'): Promise<string>;
  acceptCall(callId: string): Promise<void>;
  declineCall(callId: string): Promise<void>;
  endCall(callId: string): Promise<void>;
  
  // WebRTC signaling
  setOffer(callId: string, offer: RTCSessionDescriptionInit): Promise<void>;
  setAnswer(callId: string, answer: RTCSessionDescriptionInit): Promise<void>;
  addIceCandidate(callId: string, userId: string, candidate: RTCIceCandidateInit): Promise<void>;
  
  // Call monitoring
  subscribeToCall(callId: string, callback: (call: CallSession) => void): () => void;
  subscribeToIncomingCalls(userId: string, callback: (calls: CallSession[]) => void): () => void;
}
