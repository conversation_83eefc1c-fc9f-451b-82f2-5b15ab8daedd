import React, { createContext, useContext, useState, useEffect } from 'react';
import { 
  onAuthStateChanged, 
  // Remove all type imports
} from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { auth, db } from '../services/firebase';
import type { UserProfile, FirebaseUser } from '../types/firebase';

interface AuthContextType {
  currentUser: FirebaseUser | null;
  userProfile: UserProfile | null;
  loading: boolean;
  isAdmin: boolean;
  isDoctor: boolean;
  isStudent: boolean;
  hasRole: (roles: string[]) => boolean;
  refreshUserProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  currentUser: null,
  userProfile: null,
  loading: true,
  isAdmin: false,
  isDoctor: false,
  isStudent: false,
  hasRole: () => false,
  refreshUserProfile: async () => {}
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<FirebaseUser | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);

      if (user) {
        try {
          // Fetch user profile from Firestore
          const userDoc = await getDoc(doc(db, 'users', user.uid));

          if (userDoc.exists()) {
            const profileData = userDoc.data() as UserProfile;
            console.log('🔍 AuthContext: User profile loaded from Firestore:', {
              uid: user.uid,
              email: profileData.email,
              role: profileData.role,
              displayName: profileData.displayName
            });
            setUserProfile(profileData);
          } else {
            // If no profile exists, create a default one
            const defaultProfile: UserProfile = {
              uid: user.uid,
              email: user.email || '',
              displayName: user.displayName || user.email?.split('@')[0] || '',
              role: 'student', // Default role
              createdAt: new Date(),
              updatedAt: new Date()
            };
            setUserProfile(defaultProfile);
          }
        } catch (error) {
          console.error('Error fetching user profile:', error);
          // Set a default profile even if there's an error
          const defaultProfile: UserProfile = {
            uid: user.uid,
            email: user.email || '',
            displayName: user.displayName || user.email?.split('@')[0] || '',
            role: 'student',
            createdAt: new Date(),
            updatedAt: new Date()
          };
          setUserProfile(defaultProfile);
        }
      } else {
        setUserProfile(null);
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);

  // Role-based helper functions
  const isAdmin = userProfile?.role === 'admin';
  const isDoctor = userProfile?.role === 'doctor';
  const isStudent = userProfile?.role === 'student';
  
  // Check if user has any of the specified roles
  const hasRole = (roles: string[]) => {
    if (!userProfile) return false;
    return roles.includes(userProfile.role);
  };

  // Function to manually refresh user profile from Firestore
  const refreshUserProfile = async () => {
    if (!currentUser) return;

    try {
      const userDoc = await getDoc(doc(db, 'users', currentUser.uid));
      if (userDoc.exists()) {
        const profileData = userDoc.data() as UserProfile;
        setUserProfile(profileData);
        console.log('✅ User profile refreshed:', profileData.role);
      }
    } catch (error) {
      console.error('Error refreshing user profile:', error);
    }
  };

  const value = {
    currentUser,
    userProfile,
    loading,
    isAdmin,
    isDoctor,
    isStudent,
    hasRole,
    refreshUserProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export { AuthContext };


